# AI赋能群聊信息核查助手

基于uni-app开发的跨平台信息核查小程序，支持微信小程序、QQ小程序等多个平台。

## 功能特性

### 核心功能
- 📝 **文本核查**: 支持直接输入或粘贴文本内容进行真实性核查
- 🖼️ **图片核查**: 支持上传图片，通过OCR提取文本后进行核查
- 📱 **平台特性**: QQ小程序支持从聊天会话直接选择图片
- 📊 **结果展示**: 清晰展示核查结果、可信度评估、原始来源和发布时间
- 📝 **用户反馈**: 支持对核查结果进行准确性反馈

### 辅助功能
- 📚 **历史记录**: 本地存储核查历史，支持查看、删除和清空
- ⚙️ **设置管理**: 个性化设置，包括历史记录管理、通知设置等
- 🔄 **下拉刷新**: 历史记录页面支持下拉刷新
- 💾 **数据管理**: 完善的本地存储管理和数据清理功能

## 技术架构

### 前端技术栈
- **框架**: uni-app (Vue 3 + TypeScript)
- **UI组件**: uni-ui 组件库
- **样式**: SCSS
- **状态管理**: Vue 3 Composition API
- **构建工具**: Vite

### 项目结构
```
src/
├── components/          # 自定义组件
│   └── LoadingModal.vue # 加载模态框组件
├── pages/              # 页面文件
│   ├── index/          # 首页 - 信息输入
│   ├── result/         # 结果页 - 核查结果展示
│   ├── history/        # 历史页 - 历史记录管理
│   └── settings/       # 设置页 - 应用设置
├── static/             # 静态资源
│   └── images/         # 图标文件
├── utils/              # 工具函数
│   ├── api.ts          # API服务层
│   ├── storage.ts      # 本地存储管理
│   ├── common.ts       # 通用工具函数
│   └── error.ts        # 错误处理工具
├── App.vue             # 应用根组件
├── main.ts             # 应用入口
├── pages.json          # 页面配置
└── manifest.json       # 应用配置
```

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- HBuilderX 或 VS Code
- uni-app CLI

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
# 微信小程序
npm run dev:mp-weixin

# QQ小程序
npm run dev:mp-qq

# H5
npm run dev:h5

# App
npm run dev:app
```

### 构建发布
```bash
# 微信小程序
npm run build:mp-weixin

# QQ小程序
npm run build:mp-qq

# H5
npm run build:h5

# App
npm run build:app
```

## 配置说明

### API配置
在 `src/utils/api.ts` 中配置后端API地址：
```typescript
const API_CONFIG = {
  baseUrl: 'https://your-api-domain.com/api/v1', // 替换为实际的API地址
  timeout: 30000, // 30秒超时
}
```

### 平台特性配置
项目使用条件编译来处理平台差异：
- `#ifdef MP-QQ` - QQ小程序特有功能
- `#ifdef MP-WEIXIN` - 微信小程序特有功能
- `#ifdef H5` - H5平台特有功能
- `#ifdef MP` - 所有小程序平台

## 核心模块说明

### 1. 信息输入模块 (pages/index)
- 支持文本输入和图片上传
- 图片压缩和验证
- 平台特定的图片选择功能
- 输入验证和错误处理

### 2. 结果展示模块 (pages/result)
- 核查结果状态展示
- 信息来源和证据展示
- 用户反馈收集
- 链接复制功能

### 3. 历史记录模块 (pages/history)
- 本地历史记录管理
- 滑动删除功能
- 相对时间显示
- 批量清理功能

### 4. 设置管理模块 (pages/settings)
- 用户偏好设置
- 存储使用情况查看
- 数据管理功能
- 应用信息展示

### 5. 工具模块 (utils/)
- **API服务**: 统一的网络请求管理
- **存储管理**: 本地数据存储和管理
- **错误处理**: 全局错误处理和用户提示
- **通用工具**: 时间格式化、防抖节流等

## 数据流程

### 核查流程
1. 用户输入文本或上传图片
2. 前端验证输入数据
3. 调用后端API进行核查
4. 展示核查结果
5. 保存到本地历史记录
6. 支持用户反馈

### 数据存储
- **历史记录**: 本地存储，支持设置最大数量
- **用户设置**: 本地存储用户偏好
- **缓存数据**: 临时数据和图片缓存

## 部署说明

### 微信小程序
1. 在微信开发者工具中导入项目
2. 配置AppID和相关权限
3. 上传代码并提交审核

### QQ小程序
1. 在QQ开发者工具中导入项目
2. 配置AppID和相关权限
3. 上传代码并提交审核

### H5部署
1. 构建H5版本
2. 部署到Web服务器
3. 配置域名和HTTPS

## 注意事项

### 开发注意事项
- 使用TypeScript进行类型检查
- 遵循uni-app的开发规范
- 注意平台差异和条件编译
- 做好错误处理和用户体验

### 生产环境
- 替换API地址为生产环境
- 配置正确的AppID和权限
- 测试各平台功能完整性
- 优化性能和加载速度

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
