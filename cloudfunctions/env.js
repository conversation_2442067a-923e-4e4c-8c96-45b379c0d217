// 云开发环境配置
module.exports = {
  // 环境ID，需要替换为实际的环境ID
  envId: 'your-env-id',
  
  // 云函数配置
  functions: {
    checkText: {
      timeout: 30,
      memory: 256,
      runtime: 'Nodejs16.14'
    },
    checkImage: {
      timeout: 60,
      memory: 512,
      runtime: 'Nodejs16.14'
    },
    submitFeedback: {
      timeout: 10,
      memory: 128,
      runtime: 'Nodejs16.14'
    },
    initDatabase: {
      timeout: 30,
      memory: 256,
      runtime: 'Nodejs16.14'
    }
  },
  
  // 云对象配置
  containers: {
    newsVerifier: {
      timeout: 30,
      memory: 256,
      runtime: 'Nodejs16.14'
    }
  },
  
  // 数据库配置
  database: {
    collections: [
      'check_records',
      'user_feedback', 
      'system_config'
    ]
  },
  
  // 存储配置
  storage: {
    rules: [
      {
        resource: 'images/*',
        allow: ['create', 'read'],
        condition: 'auth.uid != null'
      }
    ]
  }
}
