// 图片核查云函数
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { fileID, options = {} } = event
    
    // 参数验证
    if (!fileID) {
      return {
        code: 400,
        message: '图片文件ID不能为空',
        error: 'INVALID_FILE_ID'
      }
    }
    
    // 生成核查ID
    const checkId = `check_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 获取图片文件
    const fileResult = await cloud.downloadFile({
      fileID: fileID
    })
    
    if (!fileResult.buffer) {
      return {
        code: 400,
        message: '无法获取图片文件',
        error: 'FILE_NOT_FOUND'
      }
    }
    
    // 调用OCR服务提取文字
    const ocrResult = await performOCR(fileResult.buffer)
    
    // 如果提取到文字，进行文本核查
    let checkResult
    if (ocrResult.text && ocrResult.text.trim().length > 0) {
      checkResult = await performTextCheck(ocrResult.text, options)
    } else {
      // 如果没有提取到文字，进行图片反向搜索
      checkResult = await performImageSearch(fileResult.buffer)
    }
    
    // 保存核查记录到数据库
    const db = cloud.database()
    await db.collection('check_records').add({
      data: {
        id: checkId,
        type: 'image',
        content: `图片核查: ${ocrResult.text ? ocrResult.text.substring(0, 100) + '...' : '无文字内容'}`,
        file_id: fileID,
        ocr_result: ocrResult,
        status: checkResult.status,
        confidence: checkResult.confidence,
        summary: checkResult.summary,
        publish_time: checkResult.publishTime,
        sources: checkResult.sources,
        evidences: checkResult.evidences,
        analysis_details: checkResult.analysis_details,
        user_openid: wxContext.OPENID,
        created_at: new Date(),
        updated_at: new Date()
      }
    })
    
    // 返回结果
    return {
      code: 200,
      message: 'success',
      data: {
        id: checkId,
        ocr_result: {
          text: ocrResult.text,
          confidence: ocrResult.confidence
        },
        status: checkResult.status,
        confidence: checkResult.confidence,
        summary: checkResult.summary,
        publishTime: checkResult.publishTime,
        sources: checkResult.sources,
        evidences: checkResult.evidences
      }
    }
    
  } catch (error) {
    console.error('图片核查失败:', error)
    
    return {
      code: 500,
      message: '核查服务暂时不可用，请稍后重试',
      error: 'SERVICE_UNAVAILABLE'
    }
  }
}

// OCR文字识别（模拟）
async function performOCR(imageBuffer) {
  // 模拟OCR处理延迟
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // 这里应该调用真实的OCR服务，如腾讯云OCR、百度OCR等
  // 目前返回模拟数据
  return {
    text: '这是从图片中识别出的文字内容示例',
    confidence: 0.92,
    regions: [
      {
        text: '这是从图片中识别出的文字内容示例',
        bbox: [100, 100, 400, 150],
        confidence: 0.92
      }
    ]
  }
}

// 执行文本核查（复用文本核查逻辑）
async function performTextCheck(text, options) {
  // 1. 关键信息提取
  const keywords = extractKeywords(text)
  
  // 2. 网络搜索
  const searchResults = await simulateWebSearch(keywords)
  
  // 3. LLM分析
  const analysis = await simulateLLMAnalysis(text, searchResults)
  
  return {
    status: analysis.status,
    confidence: analysis.confidence,
    summary: analysis.summary,
    publishTime: analysis.publishTime,
    sources: analysis.sources,
    evidences: analysis.evidences,
    analysis_details: {
      keyword_extraction: keywords,
      search_results_count: searchResults.length,
      analysis_method: 'text_from_image'
    }
  }
}

// 图片反向搜索（模拟）
async function performImageSearch(imageBuffer) {
  // 模拟图片搜索延迟
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  // 这里应该调用图片反向搜索服务
  return {
    status: 'disputed',
    confidence: 0.65,
    summary: '经核查，该图片信息存在争议。部分信息可能被断章取义或存在时间错位。',
    publishTime: '2024年1月20日',
    sources: [
      {
        title: '辟谣平台：相关图片核实',
        url: 'https://fact-check.example.com/article1'
      }
    ],
    evidences: [
      '原图片拍摄时间与传播时间不符',
      '图片内容与描述存在偏差',
      '缺乏权威来源证实'
    ],
    analysis_details: {
      reverse_search_results: [
        {
          source_url: '原始来源URL',
          similarity: 0.95,
          first_seen: '2024-01-20T08:00:00Z'
        }
      ],
      analysis_method: 'image_reverse_search'
    }
  }
}

// 关键词提取
function extractKeywords(text) {
  const words = text.split(/\s+/)
  return words.filter(word => word.length > 2).slice(0, 10)
}

// 模拟网络搜索
async function simulateWebSearch(keywords) {
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return [
    {
      title: '相关图片新闻报道',
      url: 'https://news.example.com/image-article',
      snippet: '相关图片内容摘要...',
      credibility: 0.8
    }
  ]
}

// 模拟LLM分析
async function simulateLLMAnalysis(text, searchResults) {
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  const hasReliableSource = searchResults.some(result => result.credibility > 0.8)
  const status = hasReliableSource ? 'supported' : 'insufficient'
  const confidence = hasReliableSource ? 0.75 : 0.55
  
  return {
    status,
    confidence,
    summary: status === 'supported' 
      ? '经核查，图片中的信息基本可信。找到了相关的可靠来源。'
      : '经核查，图片中的信息缺乏足够的证据支持。建议谨慎对待。',
    publishTime: '2024年2月10日',
    sources: searchResults.map(result => ({
      title: result.title,
      url: result.url
    })),
    evidences: hasReliableSource 
      ? ['找到相关的新闻报道', '图片内容与报道一致']
      : ['缺乏权威来源证实', '图片真实性有待验证']
  }
}
