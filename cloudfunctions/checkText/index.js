// 文本核查云函数
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { text, options = {} } = event
    
    // 参数验证
    if (!text || typeof text !== 'string') {
      return {
        code: 400,
        message: '文本内容不能为空',
        error: 'INVALID_TEXT'
      }
    }
    
    if (text.length < 5) {
      return {
        code: 400,
        message: '文本内容过短，请输入至少5个字符',
        error: 'TEXT_TOO_SHORT'
      }
    }
    
    if (text.length > 2000) {
      return {
        code: 400,
        message: '文本内容过长，请控制在2000字符以内',
        error: 'TEXT_TOO_LONG'
      }
    }
    
    // 生成核查ID
    const checkId = `check_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 调用核查服务
    const checkResult = await performTextCheck(text, options)
    
    // 保存核查记录到数据库
    const db = cloud.database()
    await db.collection('check_records').add({
      data: {
        id: checkId,
        type: 'text',
        content: text.length > 100 ? text.substring(0, 100) + '...' : text,
        full_content: text,
        status: checkResult.status,
        confidence: checkResult.confidence,
        summary: checkResult.summary,
        publish_time: checkResult.publishTime,
        sources: checkResult.sources,
        evidences: checkResult.evidences,
        analysis_details: checkResult.analysis_details,
        user_openid: wxContext.OPENID,
        created_at: new Date(),
        updated_at: new Date()
      }
    })
    
    // 返回结果（不包含敏感信息）
    return {
      code: 200,
      message: 'success',
      data: {
        id: checkId,
        status: checkResult.status,
        confidence: checkResult.confidence,
        summary: checkResult.summary,
        publishTime: checkResult.publishTime,
        sources: checkResult.sources,
        evidences: checkResult.evidences
      }
    }
    
  } catch (error) {
    console.error('文本核查失败:', error)
    
    return {
      code: 500,
      message: '核查服务暂时不可用，请稍后重试',
      error: 'SERVICE_UNAVAILABLE'
    }
  }
}

// 执行文本核查的核心逻辑
async function performTextCheck(text, options) {
  // 这里是模拟的核查逻辑，实际项目中需要集成真实的AI服务
  
  // 1. 关键信息提取
  const keywords = extractKeywords(text)
  
  // 2. 网络搜索（模拟）
  const searchResults = await simulateWebSearch(keywords)
  
  // 3. LLM分析（模拟）
  const analysis = await simulateLLMAnalysis(text, searchResults)
  
  // 4. 生成核查结果
  return {
    status: analysis.status,
    confidence: analysis.confidence,
    summary: analysis.summary,
    publishTime: analysis.publishTime,
    sources: analysis.sources,
    evidences: analysis.evidences,
    analysis_details: {
      keyword_extraction: keywords,
      search_results_count: searchResults.length,
      analysis_method: 'simulated'
    }
  }
}

// 关键词提取（简化版）
function extractKeywords(text) {
  // 简单的关键词提取逻辑
  const words = text.split(/\s+/)
  return words.filter(word => word.length > 2).slice(0, 10)
}

// 模拟网络搜索
async function simulateWebSearch(keywords) {
  // 模拟搜索延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return [
    {
      title: '相关新闻报道',
      url: 'https://news.example.com/article1',
      snippet: '相关内容摘要...',
      credibility: 0.9
    },
    {
      title: '官方声明',
      url: 'https://official.example.com/statement',
      snippet: '官方回应内容...',
      credibility: 0.95
    }
  ]
}

// 模拟LLM分析
async function simulateLLMAnalysis(text, searchResults) {
  // 模拟分析延迟
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // 简单的规则判断（实际应该使用LLM）
  const hasOfficialSource = searchResults.some(result => 
    result.url.includes('official') || result.credibility > 0.9
  )
  
  const status = hasOfficialSource ? 'supported' : 'disputed'
  const confidence = hasOfficialSource ? 0.85 : 0.65
  
  return {
    status,
    confidence,
    summary: status === 'supported' 
      ? '经核查，该信息基本属实。多个可靠来源证实了相关内容的真实性。'
      : '经核查，该信息存在争议。部分信息可能不够准确或缺乏权威来源证实。',
    publishTime: '2024年2月15日',
    sources: searchResults.map(result => ({
      title: result.title,
      url: result.url
    })),
    evidences: hasOfficialSource 
      ? ['多家权威媒体进行了报道', '官方机构已确认相关信息']
      : ['缺乏权威来源证实', '信息来源可信度有待验证']
  }
}
