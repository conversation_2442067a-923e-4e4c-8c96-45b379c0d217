# AI赋能群聊信息核查助手 - 后端API接口文档

## 接口概述

本文档定义了信息核查助手小程序所需的后端API接口规范。后端需要集成OCR服务、LLM服务和网络搜索服务，提供完整的信息核查能力。

## 基础信息

- **API版本**: v1
- **基础URL**: `https://your-api-domain.com/api/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: RESTful API

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "ERROR_CODE",
  "timestamp": 1640995200000
}
```

## 核心API接口

### 1. 文本核查接口

**接口地址**: `POST /check/text`

**功能描述**: 对用户输入的文本内容进行真实性核查

**请求参数**:
```json
{
  "text": "需要核查的文本内容",
  "options": {
    "enable_summary": true,
    "enable_source_trace": true,
    "enable_time_estimate": true
  }
}
```

**参数说明**:
- `text` (string, 必填): 待核查的文本内容，长度5-2000字符
- `options` (object, 可选): 核查选项配置
  - `enable_summary` (boolean): 是否生成摘要，默认true
  - `enable_source_trace` (boolean): 是否追溯来源，默认true
  - `enable_time_estimate` (boolean): 是否估算发布时间，默认true

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "check_id": "check_20240101_123456",
    "status": "supported",
    "confidence": 0.85,
    "summary": "经核查，该信息基本属实。多个可靠来源证实了该事件的真实性。",
    "publish_time": "2024年2月15日",
    "publish_time_confidence": 0.75,
    "sources": [
      {
        "title": "新华网：相关事件报道",
        "url": "https://news.example.com/article1",
        "credibility": 0.95,
        "publish_date": "2024-02-15T10:30:00Z"
      }
    ],
    "evidences": [
      "多家权威媒体对该事件进行了报道",
      "事件发生地相关部门已确认该信息"
    ],
    "analysis_details": {
      "keyword_extraction": ["关键词1", "关键词2"],
      "entity_recognition": ["实体1", "实体2"],
      "sentiment_analysis": "neutral",
      "fact_check_results": [
        {
          "claim": "具体声明",
          "verdict": "true",
          "confidence": 0.9
        }
      ]
    }
  }
}
```

### 2. 图片核查接口

**接口地址**: `POST /check/image`

**功能描述**: 对用户上传的图片进行OCR文本提取和核查

**请求参数**: 
- Content-Type: `multipart/form-data`
- `image`: 图片文件 (支持jpg, png, webp格式，最大10MB)
- `options`: JSON字符串，包含核查选项

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "check_id": "check_20240101_123457",
    "ocr_result": {
      "text": "从图片中提取的文本内容",
      "confidence": 0.92,
      "regions": [
        {
          "text": "文本块1",
          "bbox": [100, 100, 200, 150],
          "confidence": 0.95
        }
      ]
    },
    "status": "disputed",
    "confidence": 0.65,
    "summary": "经核查，该图片信息存在争议。部分信息可能被断章取义。",
    "publish_time": "2024年1月20日",
    "sources": [
      {
        "title": "辟谣平台：相关图片核实",
        "url": "https://fact-check.example.com/article1",
        "credibility": 0.88
      }
    ],
    "evidences": [
      "原图片拍摄时间与传播时间不符",
      "图片内容与描述存在偏差"
    ],
    "image_analysis": {
      "reverse_search_results": [
        {
          "source_url": "原始来源URL",
          "similarity": 0.95,
          "first_seen": "2024-01-20T08:00:00Z"
        }
      ],
      "metadata": {
        "exif_data": {},
        "creation_time": "2024-01-20T08:00:00Z",
        "modification_time": "2024-01-20T08:05:00Z"
      }
    }
  }
}
```

### 3. 用户反馈接口

**接口地址**: `POST /feedback`

**功能描述**: 收集用户对核查结果的反馈

**请求参数**:
```json
{
  "check_id": "check_20240101_123456",
  "rating": "accurate",
  "comment": "核查结果很准确",
  "user_id": "user_optional_id"
}
```

**参数说明**:
- `check_id` (string, 必填): 核查记录ID
- `rating` (string, 必填): 评价结果，可选值: "accurate", "inaccurate"
- `comment` (string, 可选): 用户评论
- `user_id` (string, 可选): 用户ID（如果有用户系统）

**响应数据**:
```json
{
  "code": 200,
  "message": "反馈提交成功",
  "data": {
    "feedback_id": "feedback_20240101_123456"
  }
}
```

### 4. 核查历史接口（可选）

**接口地址**: `GET /history`

**功能描述**: 获取用户的云端核查历史（如果支持云端存储）

**请求参数**:
- `user_id` (string, 可选): 用户ID
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认20

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "items": [
      {
        "check_id": "check_20240101_123456",
        "content": "核查内容摘要",
        "type": "text",
        "status": "supported",
        "check_time": "2024-01-01T12:34:56Z",
        "summary": "核查结果摘要"
      }
    ]
  }
}
```

## 状态码说明

### 核查状态 (status)
- `supported`: 信息可信 - 有充分证据支持该信息的真实性
- `disputed`: 信息存疑 - 存在相互矛盾的证据或信息不完整
- `insufficient`: 信息不足 - 缺乏足够的证据进行判断

### HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `413`: 文件过大
- `429`: 请求频率过高
- `500`: 服务器内部错误
- `503`: 服务暂时不可用

## 错误码说明

### 业务错误码
- `INVALID_TEXT`: 文本内容无效
- `TEXT_TOO_SHORT`: 文本内容过短
- `TEXT_TOO_LONG`: 文本内容过长
- `INVALID_IMAGE`: 图片格式不支持
- `IMAGE_TOO_LARGE`: 图片文件过大
- `OCR_FAILED`: OCR识别失败
- `ANALYSIS_FAILED`: 分析处理失败
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `SERVICE_UNAVAILABLE`: 服务暂时不可用

## 技术要求

### 1. OCR服务集成
- 支持中文文字识别
- 识别准确率 > 90%
- 支持倾斜文字矫正
- 返回文字位置信息

### 2. LLM服务集成
- 支持中文语义理解
- 实现RAG（检索增强生成）
- 支持多轮对话和上下文理解
- 提供置信度评分

### 3. 网络搜索服务
- 集成主流搜索引擎API
- 支持实时网络搜索
- 过滤和排序搜索结果
- 提取关键信息和时间戳

### 4. 性能要求
- 文本核查响应时间 < 10秒
- 图片核查响应时间 < 30秒
- 并发处理能力 > 100 QPS
- 服务可用性 > 99.5%

### 5. 安全要求
- HTTPS加密传输
- API访问频率限制
- 敏感信息过滤
- 用户数据隐私保护

## 部署建议

### 1. 架构建议
- 微服务架构，独立部署各个服务
- 使用消息队列处理异步任务
- 实现服务熔断和降级机制
- 配置负载均衡和自动扩缩容

### 2. 数据存储
- 使用Redis缓存热点数据
- 数据库存储核查结果和用户反馈
- 对象存储保存上传的图片文件
- 定期清理过期数据

### 3. 监控告警
- 接口响应时间监控
- 错误率和成功率统计
- 资源使用情况监控
- 异常情况自动告警

## 测试用例

### 1. 文本核查测试
```bash
curl -X POST "https://api.example.com/api/v1/check/text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "测试文本内容"
  }'
```

### 2. 图片核查测试
```bash
curl -X POST "https://api.example.com/api/v1/check/image" \
  -F "image=@test.jpg" \
  -F "options={\"enable_summary\":true}"
```

## 实现建议

### 1. 核查流程设计

#### 文本核查流程
1. **预处理**: 文本清洗、敏感词过滤、长度验证
2. **关键信息提取**: 使用NLP技术提取关键实体、时间、地点等
3. **搜索查询生成**: 基于关键信息生成多个搜索查询
4. **网络搜索**: 并行调用多个搜索引擎API
5. **结果分析**: LLM分析搜索结果，进行事实核查
6. **置信度计算**: 基于多个维度计算最终置信度
7. **结果生成**: 生成结构化的核查报告

#### 图片核查流程
1. **图片预处理**: 格式转换、尺寸调整、质量检查
2. **OCR文字识别**: 提取图片中的文字信息
3. **图片反向搜索**: 在网络中搜索相似图片
4. **元数据分析**: 提取EXIF信息、创建时间等
5. **文字内容核查**: 对提取的文字进行核查
6. **综合分析**: 结合图片和文字信息进行综合判断

### 2. 数据库设计

#### 核查记录表 (check_records)
```sql
CREATE TABLE check_records (
  id VARCHAR(50) PRIMARY KEY,
  type ENUM('text', 'image') NOT NULL,
  content TEXT NOT NULL,
  status ENUM('supported', 'disputed', 'insufficient') NOT NULL,
  confidence DECIMAL(3,2) NOT NULL,
  summary TEXT,
  publish_time VARCHAR(100),
  sources JSON,
  evidences JSON,
  analysis_details JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 用户反馈表 (user_feedback)
```sql
CREATE TABLE user_feedback (
  id VARCHAR(50) PRIMARY KEY,
  check_id VARCHAR(50) NOT NULL,
  rating ENUM('accurate', 'inaccurate') NOT NULL,
  comment TEXT,
  user_id VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (check_id) REFERENCES check_records(id)
);
```

### 3. 缓存策略

#### Redis缓存设计
```
# 核查结果缓存 (24小时)
check:result:{text_hash} -> JSON结果
check:result:{image_hash} -> JSON结果

# 搜索结果缓存 (1小时)
search:result:{query_hash} -> JSON搜索结果

# 频率限制 (1分钟)
rate_limit:{ip}:{minute} -> 请求次数
```

### 4. 第三方服务集成

#### OCR服务选择
- **腾讯云OCR**: 高精度中文识别
- **百度AI OCR**: 丰富的场景支持
- **阿里云OCR**: 稳定的服务质量
- **Google Vision API**: 国际化支持

#### LLM服务选择
- **OpenAI GPT**: 强大的理解和生成能力
- **百度文心一言**: 中文优化
- **阿里通义千问**: 企业级服务
- **智谱ChatGLM**: 开源可控

#### 搜索服务集成
- **百度搜索API**: 中文内容丰富
- **Google Custom Search**: 全球覆盖
- **必应搜索API**: 平衡的选择
- **搜狗搜索API**: 微信内容索引

### 5. 安全防护

#### API安全
```python
# 请求频率限制
@rate_limit(max_requests=100, window=3600)  # 每小时100次
def check_text():
    pass

# 内容安全检查
def content_security_check(text):
    # 敏感词过滤
    # 违法内容检测
    # 垃圾信息识别
    pass
```

#### 数据安全
- 用户上传图片自动删除（24小时后）
- 敏感信息脱敏处理
- 访问日志记录和审计
- 数据传输加密

### 6. 监控指标

#### 业务指标
- 核查请求量（QPS）
- 核查成功率
- 平均响应时间
- 用户反馈准确率

#### 技术指标
- 服务可用性
- 错误率统计
- 资源使用率
- 第三方服务调用成功率

### 7. 部署架构图

```
[负载均衡器]
    ↓
[API网关] → [认证服务]
    ↓
[核查服务集群]
    ↓
[OCR服务] [LLM服务] [搜索服务]
    ↓
[Redis缓存] [MySQL数据库] [对象存储]
```

## 开发优先级

### Phase 1: 核心功能 (4周)
- [ ] 文本核查API
- [ ] 图片核查API
- [ ] 基础OCR集成
- [ ] 简单的事实核查逻辑

### Phase 2: 增强功能 (3周)
- [ ] LLM服务集成
- [ ] 网络搜索集成
- [ ] 用户反馈API
- [ ] 缓存机制

### Phase 3: 优化完善 (3周)
- [ ] 性能优化
- [ ] 监控告警
- [ ] 安全加固
- [ ] 文档完善

## 版本更新

- v1.0.0: 初始版本，实现基础核查功能
- v1.1.0: 增加图片反向搜索功能
- v1.2.0: 优化LLM分析准确性
- v1.3.0: 增加批量核查支持
- 后续版本将根据用户反馈和业务需求进行迭代优化
