# 微信云开发部署指南

## 项目概述

本项目使用微信云开发作为后端服务，提供云函数、云数据库、云存储等能力，实现AI赋能的信息核查功能。

## 云开发架构

```
前端小程序
    ↓
微信云开发
├── 云函数
│   ├── checkText (文本核查)
│   ├── checkImage (图片核查)
│   ├── submitFeedback (用户反馈)
│   └── initDatabase (数据库初始化)
├── 云对象
│   └── newsVerifier (综合核查服务)
├── 云数据库
│   ├── check_records (核查记录)
│   ├── user_feedback (用户反馈)
│   └── system_config (系统配置)
└── 云存储
    └── images/ (用户上传的图片)
```

## 部署步骤

### 1. 开通云开发服务

1. 在微信开发者工具中打开项目
2. 点击工具栏的"云开发"按钮
3. 按照提示开通云开发服务
4. 创建云开发环境，记录环境ID

### 2. 配置项目

1. 修改 `project.config.json` 中的 `appid` 为你的小程序AppID
2. 修改 `cloudfunctions/env.js` 中的 `envId` 为你的云开发环境ID

### 3. 初始化云开发

在 `src/App.vue` 中添加云开发初始化代码：

```javascript
export default {
  onLaunch: function () {
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        env: 'your-env-id', // 替换为你的环境ID
        traceUser: true
      })
    }
  }
}
```

### 4. 部署云函数

在微信开发者工具中：

1. 右键点击 `cloudfunctions/checkText` 文件夹
2. 选择"创建并部署：云端安装依赖"
3. 重复以上步骤部署其他云函数：
   - `checkImage`
   - `submitFeedback`
   - `initDatabase`

### 5. 部署云对象

1. 右键点击 `cloudfunctions/newsVerifier` 文件夹
2. 选择"创建并部署：云端安装依赖"

### 6. 初始化数据库

1. 在微信开发者工具的云开发控制台中
2. 调用 `initDatabase` 云函数来创建数据库集合和索引

### 7. 配置数据库权限

在云开发控制台的数据库页面：

1. 设置 `check_records` 集合权限：
   - 读权限：仅创建者可读写
   - 写权限：仅创建者可读写

2. 设置 `user_feedback` 集合权限：
   - 读权限：仅创建者可读写
   - 写权限：仅创建者可读写

3. 设置 `system_config` 集合权限：
   - 读权限：仅管理员可读写
   - 写权限：仅管理员可读写

### 8. 配置云存储权限

在云开发控制台的存储页面：

1. 设置存储权限为"仅创建者可读写"
2. 配置图片文件的访问规则

## 云函数详解

### checkText 云函数

**功能**: 文本内容核查
**输入参数**:
```javascript
{
  text: "要核查的文本内容",
  options: {} // 可选配置
}
```

**返回结果**:
```javascript
{
  code: 200,
  message: "success",
  data: {
    id: "check_id",
    status: "supported|disputed|insufficient",
    summary: "核查结果摘要",
    publishTime: "发布时间",
    sources: [/* 来源列表 */],
    evidences: [/* 证据列表 */]
  }
}
```

### checkImage 云函数

**功能**: 图片内容核查
**输入参数**:
```javascript
{
  fileID: "云存储文件ID",
  options: {} // 可选配置
}
```

**返回结果**:
```javascript
{
  code: 200,
  message: "success", 
  data: {
    id: "check_id",
    ocr_result: {
      text: "识别出的文字",
      confidence: 0.92
    },
    status: "supported|disputed|insufficient",
    // ... 其他字段同文本核查
  }
}
```

### submitFeedback 云函数

**功能**: 提交用户反馈
**输入参数**:
```javascript
{
  check_id: "核查记录ID",
  rating: "accurate|inaccurate",
  comment: "可选的评论内容"
}
```

## 云对象详解

### newsVerifier 云对象

提供面向对象的API调用方式：

```javascript
// 调用云对象方法
wx.cloud.callContainer({
  name: 'newsVerifier',
  method: 'checkText',
  data: { text: '要核查的内容' },
  success: res => {
    console.log(res.result)
  }
})
```

**可用方法**:
- `checkText(text, options)` - 文本核查
- `checkImage(fileID, options)` - 图片核查
- `submitFeedback(checkId, rating, comment)` - 提交反馈
- `getUserHistory(page, limit)` - 获取用户历史

## 数据库设计

### check_records 集合

```javascript
{
  _id: "自动生成",
  id: "check_20240101_123456", // 业务ID
  type: "text|image",
  content: "内容摘要",
  full_content: "完整内容(仅文本)",
  file_id: "文件ID(仅图片)",
  status: "supported|disputed|insufficient",
  confidence: 0.85,
  summary: "核查结果摘要",
  publish_time: "发布时间",
  sources: [/* 来源数组 */],
  evidences: [/* 证据数组 */],
  analysis_details: {/* 分析详情 */},
  user_openid: "用户OpenID",
  feedback_stats: {
    accurate: 0,
    inaccurate: 0,
    total: 0
  },
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

### user_feedback 集合

```javascript
{
  _id: "自动生成",
  id: "feedback_20240101_123456",
  check_id: "关联的核查记录ID",
  rating: "accurate|inaccurate",
  comment: "用户评论",
  user_openid: "用户OpenID",
  created_at: "创建时间"
}
```

## 第三方服务集成

### OCR服务集成

推荐使用腾讯云OCR：

```javascript
// 在云函数中集成腾讯云OCR
const tencentcloud = require("tencentcloud-sdk-nodejs")

const OcrClient = tencentcloud.ocr.v20181119.Client

const client = new OcrClient({
  credential: {
    secretId: "your-secret-id",
    secretKey: "your-secret-key",
  },
  region: "ap-beijing",
})

// 调用OCR接口
const params = {
  ImageBase64: imageBase64
}

const data = await client.GeneralBasicOCR(params)
```

### LLM服务集成

推荐使用OpenAI API或国内的LLM服务：

```javascript
// 集成OpenAI API
const openai = require('openai')

const client = new openai({
  apiKey: 'your-api-key'
})

const completion = await client.chat.completions.create({
  messages: [
    { role: "system", content: "你是一个专业的信息核查助手" },
    { role: "user", content: `请核查以下信息的真实性：${text}` }
  ],
  model: "gpt-3.5-turbo",
})
```

## 监控和日志

### 云函数日志

在云开发控制台可以查看：
- 函数调用次数
- 执行时间
- 错误日志
- 自定义日志

### 性能监控

监控指标：
- 函数执行时间
- 数据库查询性能
- 存储使用量
- 用户活跃度

## 安全配置

### 1. 环境变量

在云开发控制台设置环境变量：
- `OCR_SECRET_ID`
- `OCR_SECRET_KEY`
- `LLM_API_KEY`
- `SEARCH_API_KEY`

### 2. 访问控制

- 设置合适的数据库权限
- 配置云存储访问规则
- 实现API调用频率限制

### 3. 数据安全

- 敏感信息加密存储
- 定期清理过期数据
- 用户数据脱敏处理

## 成本优化

### 1. 资源配置

- 根据实际需求调整云函数内存和超时时间
- 使用合适的数据库索引
- 优化图片存储策略

### 2. 缓存策略

- 实现查询结果缓存
- 使用CDN加速静态资源
- 合理设置缓存过期时间

## 故障排查

### 常见问题

1. **云函数调用失败**
   - 检查函数是否正确部署
   - 查看函数执行日志
   - 验证参数格式

2. **数据库操作失败**
   - 检查集合权限设置
   - 验证数据格式
   - 查看数据库日志

3. **文件上传失败**
   - 检查存储权限
   - 验证文件大小和格式
   - 查看网络连接

### 调试技巧

1. 使用云开发控制台的在线调试功能
2. 在云函数中添加详细的日志输出
3. 使用微信开发者工具的网络面板查看请求

## 版本更新

### 灰度发布

1. 创建新版本的云函数
2. 配置流量分配
3. 监控新版本性能
4. 逐步切换全部流量

### 数据迁移

1. 备份现有数据
2. 执行数据库结构变更
3. 验证数据完整性
4. 更新应用代码

## 总结

微信云开发为小程序提供了完整的后端服务能力，通过合理的架构设计和配置，可以快速构建稳定可靠的信息核查服务。建议在开发过程中：

1. 充分利用云开发的各项能力
2. 注意安全和性能优化
3. 做好监控和日志记录
4. 定期备份重要数据

通过以上配置，您就可以成功部署和运行基于微信云开发的AI信息核查助手了。
