# 微信云开发快速开始指南

## 概述

本项目已经为您搭建好了完整的微信云开发后端框架，包括云函数、云对象、数据库设计等。您只需要按照以下步骤进行配置和部署即可。

## 🚀 快速部署（5分钟上手）

### 1. 开通云开发服务

1. 在微信开发者工具中打开项目
2. 点击工具栏的"云开发"按钮
3. 按照提示开通云开发服务
4. 创建云开发环境，记录环境ID（如：`cloud1-xxx`）

### 2. 配置环境ID

修改以下文件中的环境ID：

**src/App.vue**
```javascript
wx.cloud.init({
  env: 'your-env-id', // 替换为你的环境ID
  traceUser: true
})
```

**project.config.json**
```json
{
  "appid": "your-app-id" // 替换为你的小程序AppID
}
```

### 3. 一键部署云函数

在微信开发者工具中，右键点击以下文件夹并选择"创建并部署：云端安装依赖"：

- `cloudfunctions/checkText` ✅ 文本核查
- `cloudfunctions/checkImage` ✅ 图片核查  
- `cloudfunctions/submitFeedback` ✅ 用户反馈
- `cloudfunctions/initDatabase` ✅ 数据库初始化
- `cloudfunctions/newsVerifier` ✅ 云对象（可选）

### 4. 初始化数据库

1. 在云开发控制台的"云函数"页面
2. 找到 `initDatabase` 函数
3. 点击"测试"按钮执行，创建数据库集合和索引

### 5. 测试运行

现在您可以在小程序中测试以下功能：
- ✅ 文本核查
- ✅ 图片核查
- ✅ 历史记录
- ✅ 用户反馈

## 📁 项目结构

```
cloudfunctions/                 # 云函数目录
├── checkText/                  # 文本核查云函数
│   ├── index.js               # 主要逻辑
│   └── package.json           # 依赖配置
├── checkImage/                 # 图片核查云函数
│   ├── index.js               # 主要逻辑
│   └── package.json           # 依赖配置
├── submitFeedback/             # 用户反馈云函数
│   ├── index.js               # 主要逻辑
│   └── package.json           # 依赖配置
├── initDatabase/               # 数据库初始化云函数
│   ├── index.js               # 主要逻辑
│   └── package.json           # 依赖配置
├── newsVerifier/               # 云对象（推荐）
│   ├── index.obj.js           # 云对象主文件
│   └── package.json           # 依赖配置
└── env.js                      # 环境配置
```

## 🔧 核心功能

### 1. 文本核查 API

```javascript
// 前端调用
const result = await api.checkText('要核查的文本内容')

// 返回结果
{
  id: "check_20240101_123456",
  status: "supported", // supported|disputed|insufficient
  summary: "核查结果摘要",
  publishTime: "2024年2月15日",
  sources: [{ title: "来源标题", url: "来源链接" }],
  evidences: ["证据1", "证据2"]
}
```

### 2. 图片核查 API

```javascript
// 前端调用
const result = await api.checkImage('/path/to/image.jpg')

// 返回结果
{
  id: "check_20240101_123457",
  ocr_result: {
    text: "识别出的文字",
    confidence: 0.92
  },
  status: "disputed",
  summary: "核查结果摘要",
  // ... 其他字段同文本核查
}
```

### 3. 用户反馈 API

```javascript
// 前端调用
await api.submitFeedback('check_id', 'accurate') // 或 'inaccurate'
```

## 🗄️ 数据库设计

项目自动创建以下数据库集合：

### check_records（核查记录）
- 存储所有的核查结果
- 包含文本/图片内容、核查状态、来源等
- 按用户OpenID隔离数据

### user_feedback（用户反馈）
- 存储用户对核查结果的评价
- 用于改进算法准确性

### system_config（系统配置）
- 存储应用配置信息
- API密钥、限制参数等

## 🔌 第三方服务集成

### 当前状态（模拟数据）
项目目前使用模拟数据，可以正常运行和测试所有功能。

### 生产环境集成
要在生产环境中使用，需要集成以下服务：

#### 1. OCR服务（图片文字识别）
推荐服务：
- 腾讯云OCR ⭐️ 推荐
- 百度AI OCR
- 阿里云OCR

#### 2. LLM服务（智能分析）
推荐服务：
- OpenAI GPT ⭐️ 推荐
- 百度文心一言
- 阿里通义千问

#### 3. 搜索服务（信息检索）
推荐服务：
- 百度搜索API ⭐️ 推荐
- Google Custom Search
- 必应搜索API

### 集成示例

在云函数中添加真实服务调用：

```javascript
// 腾讯云OCR集成示例
const tencentcloud = require("tencentcloud-sdk-nodejs")
const OcrClient = tencentcloud.ocr.v20181119.Client

// OpenAI集成示例  
const openai = require('openai')
const client = new openai({ apiKey: 'your-api-key' })
```

## ⚙️ 配置说明

### 环境变量设置

在云开发控制台设置以下环境变量：

```
OCR_SECRET_ID=your_ocr_secret_id
OCR_SECRET_KEY=your_ocr_secret_key
LLM_API_KEY=your_llm_api_key
SEARCH_API_KEY=your_search_api_key
```

### 数据库权限

建议设置：
- `check_records`: 仅创建者可读写
- `user_feedback`: 仅创建者可读写  
- `system_config`: 仅管理员可读写

### 云存储权限

建议设置：
- 图片文件：仅创建者可读写
- 自动清理：24小时后删除

## 🚨 注意事项

### 1. 成本控制
- 云函数调用量
- 数据库读写次数
- 云存储空间使用
- 第三方API调用费用

### 2. 性能优化
- 合理设置云函数内存和超时时间
- 使用数据库索引优化查询
- 实现结果缓存机制

### 3. 安全考虑
- 设置API调用频率限制
- 敏感信息环境变量存储
- 用户数据隐私保护

## 📊 监控和维护

### 云开发控制台监控
- 函数调用统计
- 数据库使用情况
- 存储空间使用
- 错误日志查看

### 建议监控指标
- 核查成功率
- 平均响应时间
- 用户反馈准确率
- 资源使用情况

## 🆘 常见问题

### Q: 云函数调用失败？
A: 检查函数是否正确部署，查看云开发控制台的函数日志

### Q: 数据库操作失败？
A: 检查数据库权限设置，确认集合是否已创建

### Q: 图片上传失败？
A: 检查云存储权限，确认文件大小不超过限制

### Q: 如何查看详细日志？
A: 在云开发控制台的"云函数"页面查看函数执行日志

## 🎯 下一步

1. **测试功能**：在小程序中测试所有核查功能
2. **集成真实服务**：根据需要集成OCR、LLM等服务
3. **优化性能**：根据使用情况调整配置
4. **监控运营**：设置监控告警，关注用户反馈

## 📞 技术支持

如果遇到问题，可以：
1. 查看微信云开发官方文档
2. 在微信开发者社区提问
3. 查看项目中的详细文档

---

🎉 **恭喜！** 您已经成功搭建了一个完整的AI信息核查助手后端服务！
