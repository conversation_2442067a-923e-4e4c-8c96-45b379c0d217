**产品需求文档（PRD）**。

## 产品需求文档：AI赋能群聊信息核查助手

**1. 引言**

*   **1.1. 文档目的**
    本文档旨在明确“AI赋能群聊信息核查助手”小程序（以下简称“本产品”）的产品需求，为设计、开发、测试和部署提供依据。
*   **1.2. 项目背景**
    在微信、QQ等社交平台的群聊环境中，用户常接收到大量形式多样（文字、图片、聊天截图等）且来源不明的信息，其中不乏虚假新闻和过时信息。用户难以快速有效地辨别其真伪及原始发布时间。
*   **1.3. 产品范围**
    本产品是一款基于AI技术（OCR、LLM）的微信/QQ小程序，旨在帮助用户快速提取、分析并核查群聊中接收到的信息，判断其真实性并追溯原始发布时间。
*   **1.4. 名词解释**
    *   PRD: 产品需求文档 (Product Requirement Document)
    *   AI: 人工智能 (Artificial Intelligence)
    *   OCR: 光学字符识别 (Optical Character Recognition)
    *   LLM: 大语言模型 (Large Language Model)
    *   MVP: 最小可行产品 (Minimum Viable Product)
    *   RAG: 检索增强生成 (Retrieval-Augmented Generation)

**2. 产品概述**

*   **2.1. 产品愿景**
    成为微信和QQ用户在群聊场景下首选的、便捷可靠的信息真伪与溯源工具，提升用户的信息辨别能力，助力营造更健康的社交信息环境。
*   **2.2. 产品目标**
    *   **核心目标**：为用户提供对群聊中转发的文本及图片消息（尤其是聊天截图和新闻类图片）进行快速真伪判断和原始发布时间估算的服务。
    *   **关键价值**：
        *   **快速便捷**：用户可在社交应用内快速完成核查，无需跳转。
        *   **多格式支持**：处理文本、图片、聊天截图等多种常见消息形式。
        *   **AI驱动洞察**：利用OCR和LLM技术，提供基于网络搜索的分析结果。
        *   **时间溯源**：重点关注并尝试确定信息的原始发布时间。
        *   **提升媒介素养**：帮助用户在信息泛滥的环境中做出更明智的判断。
*   **2.3. 目标用户**
    *   **主要用户**：
        *   活跃于微信、QQ群聊，频繁接收各类转发信息的用户。
        *   对信息真实性有一定敏感度，愿意花费少量时间进行核实的用户。
        *   具备使用小程序完成此类任务的基本数字素养的用户。
    *   **次要用户（需关注易用性）**：
        *   易受健康、安全类谣言影响的中老年用户群体 [1]（前提是产品交互设计极致简洁）。

**3. 功能需求**

*   **3.1. 用户端功能**
    *   **3.1.1. 信息输入模块**
        *   **FR1.1：文本输入**
            *   **用户故事**：作为用户，我希望能直接粘贴文本内容到小程序中进行核查，以便快速验证文字消息。
            *   **需求描述**：提供文本输入框，允许用户粘贴待核查的文本信息。
        *   **FR1.2：图片上传（从相册）**
            *   **用户故事**：作为用户，我希望能从手机相册选择图片上传至小程序进行核查，以便验证图片形式的信息。
            *   **需求描述**：支持用户通过调用系统相册选择图片进行上传 [2]。
        *   **FR1.3：图片上传（从聊天会话 - QQ平台优先）**
            *   **用户故事**：作为QQ用户，我希望能直接从QQ聊天会话中选择图片转发到小程序进行分析，以获得最流畅的核查体验 [2]。
            *   **需求描述 (QQ)**：支持用户通过 `qq.chooseMessageFile` API (type: 'image') 从QQ聊天会话中选择图片文件进行上传 [2]。
            *   **需求描述 (微信)**：鉴于微信小程序直接从聊天会话获取任意图片的API受限 [5, 6]，初期引导用户先保存图片至相册，再通过FR1.2方式上传。
    *   **3.1.2. 内容提取模块 (OCR)**
        *   **FR2.1：图片文字提取**
            *   **用户故事**：作为用户，当我上传图片后，我希望小程序能自动识别并提取图片中的文字内容，以便进行后续分析。
            *   **需求描述**：对用户上传的图片（包括新闻截图、聊天记录截图等）进行OCR处理，提取其中的文本信息。
            *   **关键能力**：需有效处理中文文本，适应不同字体、背景（包括彩色或图片背景）、一定程度的图像噪声和常见的聊天界面元素干扰 [7, 8, 9]。
            *   **备选方案**：可以考虑利用多模态LLM提取信息和识别，理解图片图像层面的含义。
        *   **FR2.2：OCR结果展示与修正 (可选/低置信度时)**
            *   **用户故事**：作为用户，如果OCR识别的文字不完全准确，我希望能看到识别结果并有机会进行修正，以确保分析的准确性。
            *   **需求描述**：在OCR处理后，可选择性地向用户展示提取的文本。当OCR引擎报告的置信度较低时，应考虑提供用户预览并编辑提取文本的功能。
    *   **3.1.3. AI分析与核查模块**
        *   **FR3.1：文本理解与摘要**
            *   **用户故事**：作为用户，我希望AI能理解输入文本的核心内容和主张，以便进行准确的网络搜索和分析。
            *   **需求描述**：利用LLM对OCR提取的文本或用户直接输入的文本进行内容理解，提取关键实体（人名、组织、地点、事件等），并生成核心主张的摘要 [10, 11]。
        *   **FR3.2：网络搜索与信息检索**
            *   **用户故事**：作为用户，我希望AI能根据文本内容自动在互联网上搜索相关信息，以找到佐证或反驳的证据。
            *   **需求描述**：基于LLM生成的摘要和实体，构建搜索查询，通过搜索引擎API进行定向网络搜索，检索相关信息。采用MCP工具，调用第三方网络搜索API。
        *   **FR3.3：真伪判断与可信度评估**
            *   **用户故事**：作为用户，我希望AI能分析搜索到的信息，并对原始消息的真实性给出一个判断。
            *   **需求描述**：LLM分析检索到的多个信息来源，与原始消息的主张进行比对，评估其真实性（如：真实、虚假、误导性、无法判断等）。
        *   **FR3.4：原始来源追溯**
            *   **用户故事**：作为用户，我希望能知道这条消息最初是从哪里来的，哪个是相对可靠的原始出处。
            *   **需求描述**：LLM尝试从搜索结果中识别并判定信息的原始或早期可信来源。
        *   **FR3.5：原始发布时间估算**
            *   **用户故事**：作为用户，我非常想知道这条消息是什么时候首次发布的，以判断其时效性。
            *   **需求描述**：LLM分析各搜索结果中的时间戳及元数据，结合来源可信度，估算信息的原始发布日期或时间范围 [14, 15]。处理聚合新闻、转载（可能修改时间戳）等复杂情况 [16, 17]。
    *   **3.1.4. 结果展示模块**
        *   **FR4.1：综合评估结果**
            *   **用户故事**：作为用户，我希望能一目了然地看到核查的主要结论。
            *   **需求描述**：以清晰、简洁的方式展示综合评估结果，包括：
                *   **真实性指示器**：例如颜色标记（绿/黄/红）、明确的标签（如“高度可信”、“疑似虚假”、“信息不足”）或置信度评分。
                *   **核心摘要**：LLM生成的关于内容被判定为特定真实性等级的原因摘要。
                *   **预估原始发布日期/时间线**：清晰标示信息首次出现的大致时间。
                *   **主要判定来源**：列出1-2个支持结论的关键原始或早期可信信息来源的链接。
                *   **佐证材料**：提供2-3个支持结论或展示不同观点的网络链接。
                *   **透明度说明**：简要解释处理流程（例如，“我们识别了图片文字，搜索了网络，并分析了X个来源”）。
    *   **3.1.5. 用户反馈模块**
        *   **FR5.1：结果反馈**
            *   **用户故事**：作为用户，如果我认为核查结果不准确，我希望能反馈我的意见。
            *   **需求描述**：允许用户对单次核查结果的准确性进行评价（例如，点赞/点踩，或选择“准确”、“不准确”等）。
    *   **3.1.6. 历史记录模块**
        *   **FR6.1：查看历史**
            *   **用户故事**：作为用户，我希望能查看我之前核查过的记录和结果。
            *   **需求描述**：在小程序本地存储用户最近的核查历史，允许用户查看。
*   **3.2. 后台系统需求 (支撑功能)**
    *   **FR7.1：OCR服务接口**：提供稳定、高准确率的OCR服务，特别是针对中文聊天截图。
    *   **FR7.2：LLM服务接口**：提供内容理解、摘要、搜索查询生成、信息比对、真伪判断、来源追溯、日期估算等能力的LLM服务。
    *   **FR7.3：网络搜索服务接口**：集成MCP工具的搜索引擎API，支持根据LLM生成的查询进行网络内容检索。
    *   **FR7.4：配置管理**：支持对提示词、模型版本、可信来源权重等参数进行管理和调整。
    *   **FR7.5：用户反馈数据收集与分析**：收集用户反馈数据，用于模型迭代和产品改进。

**4. 用户交互流程 (示意)**

1.  **启动小程序与选择输入**：
    *   用户打开小程序。
    *   首页提供清晰的输入选项：“粘贴文本”、“上传图片”（微信端：从相册/相机；QQ端：从相册/相机/聊天会话 [2]）。
2.  **内容提交与处理**：
    *   用户粘贴文本或选择/拍摄/导入图片。
    *   **图片处理**：小程序显示“正在提取文字…”的加载提示。
    *   **AI分析**：小程序显示“正在分析信息并联网核实，请稍候…”的加载提示。
3.  **结果展示**：
    *   分析完成后，页面跳转至结果报告卡。
    *   清晰展示FR4.1中定义的各项结果。
    *   提供FR5.1的用户反馈入口。
4.  **历史记录访问**：
    *   用户可通过特定入口（如“我的核查”）访问FR6.1定义的历史记录列表。

**5. 非功能需求**

*   **5.1. 性能**
    *   **NFR1.1：响应速度**：从用户提交信息到展示初步结果的时间应尽可能短，目标在数秒到一分钟内，避免用户长时间等待。
    *   **NFR1.2：并发处理**：系统需能应对一定数量的并发用户请求。
*   **5.2. 准确性与可靠性**
    *   **NFR2.1：OCR准确率**：针对中文印刷体（包括常见截图字体）的识别准确率应达到较高水平（如90%以上），并持续优化对复杂背景、低质量图片的识别能力 [18]。
    *   **NFR2.2：LLM分析可靠性**：
        *   减少LLM幻觉：通过优化的提示工程、交叉验证等手段降低LLM捏造事实的概率 [19, 20, 16]。
        *   结果可复现性：对于相同输入，在无外部信息变化的情况下，应尽可能产生一致的分析结果。
        *   偏见控制：努力减少模型因训练数据可能存在的偏见而产生的有偏分析结果。
    *   **NFR2.3：来源追溯与日期判断准确性**：在可获取信息范围内，力求准确判断原始来源和发布日期，对不确定性进行标注。
*   **5.3. 易用性**
    *   **NFR3.1：操作流程简洁**：用户完成一次核查的操作步骤应尽可能少，界面引导清晰。
    *   **NFR3.2：结果易于理解**：核查结果的呈现方式应直观易懂，避免使用过多的专业术语，特别是要考虑到中老年用户的理解能力 [1]。
    *   **NFR3.3：跨平台一致性 (理想状态)**：尽可能在微信和QQ小程序端提供相似的核心用户体验，明确告知平台差异导致的功能不同点。
*   **5.4. 安全性**
    *   **NFR4.1：数据传输安全**：用户数据在客户端与服务器之间传输时必须加密。
    *   **NFR4.2：后端服务安全**：后端API接口需进行严格的身份验证和权限控制，防止未授权访问和恶意攻击。
    *   **NFR4.3：依赖服务安全**：对第三方API（如OCR、LLM、搜索）的调用需确保其安全性。
*   **5.5. 隐私性**
    *   **NFR5.1：用户同意**：在收集和处理用户数据（特别是上传的图片和文本内容）前，必须获得用户明确授权同意，并清晰告知数据用途 [21, 22]。
    *   **NFR5.2：数据最小化**：仅收集和处理与核查功能直接相关的必要信息。
    *   **NFR5.3：数据存储与删除**：制定明确的数据存储策略。用户上传的原始图片和文本内容，在完成分析后，除非用户明确同意或用于模型优化（需匿名化处理），否则不应长期保存。用户应有权请求删除其个人数据和核查历史。
    *   **NFR5.4：匿名化与聚合**：用于模型改进的数据必须进行彻底的匿名化和聚合处理。
*   **5.6. 合规性**
    *   **NFR6.1：遵守平台政策**：严格遵守微信和QQ小程序平台的开发者协议、运营规范、数据隐私和内容安全政策 [21, 22]。
    *   **NFR6.2：内容安全**：小程序自身不能成为新的不实信息传播源。需对AI生成的结果进行一定的审核或风险提示，避免因AI误判导致传播错误信息。对用户输入内容也应有基本的敏感词过滤。

**6. 技术选型与架构考量**

*   **6.1. 客户端**：微信小程序、QQ小程序。
*   **6.2. OCR技术**：
    *   优先考虑高准确率的云端OCR服务，特别是针对中文优化、能处理复杂场景（如聊天截图）的服务（例如腾讯云OCR [18]）。
    *   评估本地OCR方案（如微信小程序原生OCR能力 [23]）在特定场景的性能和限制。
*   **6.3. LLM技术**：
    *   选择在内容理解、摘要、推理、信息检索方面表现优秀的LLM。
    *   采用RAG架构，结合外部网络搜索能力，而非仅依赖LLM的参数化知识 [12, 13]。
    *   考虑使用支持工具调用（Function Calling）的LLM，以便更好地集成搜索等外部能力。
*   **6.4. 后端服务**：
    *   需构建稳定、可扩展的后端服务来承载OCR、LLM调用、搜索API集成、业务逻辑处理等。
    *   API接口设计需安全、高效。
*   **6.5. 数据库**：用于存储用户信息（如授权、设置）、核查历史（若用户选择保存）、反馈数据等。
*   **6.6. 搜索引擎集成**：集成成熟的搜索引擎API。

**7. MVP (最小可行产品) 范围**

*   **核心功能**：
    *   图片输入（微信端：从相册/相机；QQ端：从相册/相机/聊天 [2]）。
    *   文本输入（粘贴）。
    *   核心OCR文字提取。
    *   基于提取文本的LLM驱动网络搜索。
    *   基础真实性评估（例如：有证据支持/有证据反驳/信息不足）。
    *   展示1-2个关键的支持性/反驳性来源链接。
    *   初步的原始发布日期估算（若能较有把握地找到）。
*   **平台选择**：可考虑优先在QQ平台进行MVP试点，以利用其更便捷的聊天图片输入API [2]。
*   **用户体验**：流程简洁，结果展示清晰。
*   **技术验证**：重点验证OCR-LLM-搜索-结果呈现的核心链路的准确性和基本性能。

**8. 未来迭代方向**

*   **功能增强**：
    *   更精细化的真实性分类（如“断章取义”、“讽刺内容”）。
    *   信源可信度分析与展示。
    *   对已核查信息的状态更新提醒（如后续出现权威辟谣）。
    *   支持视频、音频等更多内容格式的核查。
    *   引入用户社群校验机制。
*   **体验优化**：
    *   持续优化响应速度。
    *   提供更个性化的设置选项。
    *   针对特定用户群体（如老年人）的无障碍设计优化。
*   **技术升级**：
    *   持续跟进并引入更先进的OCR和LLM模型。
    *   优化RAG策略，提升检索效率和准确性。
    *   探索AI在识别深度伪造内容方面的应用 [24, 25]。
*   **生态扩展**：
    *   与官方辟谣平台或权威机构合作，共享数据或能力（若有API）。
    *   提供API供其他应用集成。