# 后端API需求总结

## 概述

根据前端项目实现，后端需要提供以下核心API接口来支持AI赋能群聊信息核查助手的完整功能。

## 核心API接口

### 1. 文本核查接口

**接口**: `POST /api/v1/check/text`

**功能**: 对用户输入的文本进行真实性核查

**请求参数**:
```json
{
  "text": "需要核查的文本内容"
}
```

**响应数据**:
```json
{
  "id": "check_20240101_123456",
  "status": "supported|disputed|insufficient",
  "summary": "核查结果摘要",
  "publishTime": "2024年2月15日",
  "sources": [
    {
      "title": "来源标题",
      "url": "来源链接"
    }
  ],
  "evidences": ["证据1", "证据2"]
}
```

### 2. 图片核查接口

**接口**: `POST /api/v1/check/image`

**功能**: 对用户上传的图片进行OCR识别和核查

**请求参数**: 
- `image`: 图片文件 (multipart/form-data)

**响应数据**:
```json
{
  "id": "check_20240101_123457",
  "status": "supported|disputed|insufficient",
  "summary": "核查结果摘要",
  "publishTime": "2024年1月20日",
  "sources": [
    {
      "title": "来源标题",
      "url": "来源链接"
    }
  ],
  "evidences": ["证据1", "证据2"]
}
```

### 3. 用户反馈接口

**接口**: `POST /api/v1/feedback`

**功能**: 收集用户对核查结果的反馈

**请求参数**:
```json
{
  "check_id": "check_20240101_123456",
  "rating": "accurate|inaccurate"
}
```

**响应数据**:
```json
{
  "success": true
}
```

## 技术要求

### 1. OCR服务集成
- 支持中文文字识别
- 识别准确率 > 90%
- 支持常见图片格式 (jpg, png, webp)
- 文件大小限制 < 10MB

### 2. LLM服务集成
- 支持中文语义理解和分析
- 能够进行事实核查和真实性判断
- 提供置信度评分
- 生成结构化的分析结果

### 3. 网络搜索服务
- 集成搜索引擎API (百度、Google等)
- 支持实时信息搜索
- 提取相关新闻和官方信息
- 识别信息发布时间

### 4. 性能要求
- 文本核查响应时间 < 10秒
- 图片核查响应时间 < 30秒
- 支持并发请求处理
- 99%以上的服务可用性

## 核查状态说明

- `supported`: 信息可信 - 有充分证据支持该信息的真实性
- `disputed`: 信息存疑 - 存在相互矛盾的证据或信息不完整  
- `insufficient`: 信息不足 - 缺乏足够的证据进行判断

## 错误处理

### HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `413`: 文件过大
- `429`: 请求频率过高
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "ERROR_CODE"
}
```

## 安全要求

### 1. 请求限制
- API访问频率限制 (每小时100次)
- 文件大小限制 (10MB)
- 文本长度限制 (5-2000字符)

### 2. 内容安全
- 敏感词过滤
- 违法内容检测
- 用户数据隐私保护

### 3. 数据安全
- HTTPS加密传输
- 上传图片定期清理
- 访问日志记录

## 推荐技术栈

### 后端框架
- **Python**: Flask/FastAPI + Celery
- **Node.js**: Express + Bull Queue
- **Java**: Spring Boot + RabbitMQ
- **Go**: Gin + Redis Queue

### 第三方服务
- **OCR**: 腾讯云OCR、百度AI OCR、阿里云OCR
- **LLM**: OpenAI GPT、百度文心一言、阿里通义千问
- **搜索**: 百度搜索API、Google Custom Search

### 数据存储
- **数据库**: MySQL/PostgreSQL (存储核查结果)
- **缓存**: Redis (缓存搜索结果)
- **文件存储**: 阿里云OSS/腾讯云COS (图片存储)

## 部署建议

### 1. 架构设计
```
负载均衡器 → API网关 → 核查服务集群
                    ↓
            OCR服务 + LLM服务 + 搜索服务
                    ↓
            Redis缓存 + MySQL数据库 + 对象存储
```

### 2. 容器化部署
- 使用Docker容器化部署
- Kubernetes集群管理
- 自动扩缩容配置
- 健康检查和故障恢复

### 3. 监控告警
- API响应时间监控
- 错误率统计
- 资源使用监控
- 异常情况告警

## 开发优先级

### Phase 1: 核心功能 (4周)
1. 文本核查API基础实现
2. 图片核查API基础实现
3. OCR服务集成
4. 简单的事实核查逻辑

### Phase 2: 增强功能 (3周)
1. LLM服务深度集成
2. 网络搜索服务集成
3. 用户反馈API实现
4. 缓存机制优化

### Phase 3: 优化完善 (3周)
1. 性能优化和压力测试
2. 监控告警系统
3. 安全加固
4. 文档和测试完善

## 测试建议

### 1. 功能测试
- 各API接口功能验证
- 边界条件测试
- 错误处理测试

### 2. 性能测试
- 并发请求压力测试
- 响应时间测试
- 资源使用测试

### 3. 安全测试
- SQL注入测试
- XSS攻击测试
- 文件上传安全测试

## 总结

后端API的核心是提供稳定、高效的信息核查服务，需要集成多个AI服务来实现文本分析、图片识别和事实核查功能。建议采用微服务架构，确保系统的可扩展性和可维护性。

前端已经完成了完整的用户界面和交互逻辑，只需要后端提供上述API接口即可实现完整的信息核查功能。
