<template>
	<uni-popup ref="popup" type="center" :mask-click="false">
		<view class="loading-modal">
			<view class="loading-content">
				<uni-load-more status="loading" :content-text="loadingText" />
				<text class="loading-tip">{{ tip }}</text>
				<view class="loading-steps" v-if="showSteps">
					<view 
						v-for="(step, index) in steps" 
						:key="index"
						class="step-item"
						:class="{ active: currentStep >= index, completed: currentStep > index }"
					>
						<view class="step-icon">
							<uni-icons 
								v-if="currentStep > index" 
								type="checkmarkempty" 
								size="16" 
								color="#07C160" 
							/>
							<text v-else class="step-number">{{ index + 1 }}</text>
						</view>
						<text class="step-text">{{ step }}</text>
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue'

export default defineComponent({
	name: 'LoadingModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		tip: {
			type: String,
			default: '正在处理中...'
		},
		showSteps: {
			type: Boolean,
			default: false
		},
		steps: {
			type: Array as () => string[],
			default: () => ['提取内容', '分析信息', '搜索验证', '生成结果']
		},
		currentStep: {
			type: Number,
			default: 0
		}
	},
	setup(props) {
		const popup = ref<any>(null)
		
		const loadingText = {
			contentdown: '上拉显示更多',
			contentrefresh: '正在加载...',
			contentnomore: '没有更多数据了'
		}
		
		// 监听visible变化
		watch(() => props.visible, (newVal) => {
			if (popup.value) {
				if (newVal) {
					popup.value.open()
				} else {
					popup.value.close()
				}
			}
		})
		
		return {
			popup,
			loadingText
		}
	}
})
</script>

<style lang="scss" scoped>
.loading-modal {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 60rpx 40rpx;
	min-width: 500rpx;
	max-width: 600rpx;
	
	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.loading-tip {
			margin-top: 20rpx;
			font-size: 28rpx;
			color: #666666;
			text-align: center;
		}
		
		.loading-steps {
			margin-top: 40rpx;
			width: 100%;
			
			.step-item {
				display: flex;
				align-items: center;
				margin: 20rpx 0;
				
				.step-icon {
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					background-color: #F5F5F5;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;
					
					.step-number {
						font-size: 24rpx;
						color: #999999;
					}
				}
				
				.step-text {
					font-size: 28rpx;
					color: #999999;
				}
				
				&.active {
					.step-icon {
						background-color: #007AFF;
						
						.step-number {
							color: #FFFFFF;
						}
					}
					
					.step-text {
						color: #333333;
					}
				}
				
				&.completed {
					.step-icon {
						background-color: #07C160;
					}
					
					.step-text {
						color: #07C160;
					}
				}
			}
		}
	}
}
</style>
