/**
 * 本地存储管理工具
 * 管理历史记录、用户设置等本地数据
 */

// 存储键名常量
const STORAGE_KEYS = {
  CHECK_HISTORY: 'checkHistory',
  USER_SETTINGS: 'userSettings',
  LAST_CHECK_TIME: 'lastCheckTime'
}

// 历史记录项类型
export interface HistoryItem {
  id: string
  content: string
  type: 'text' | 'image'
  status: 'supported' | 'disputed' | 'insufficient'
  checkTime: number
  result: any
}

// 用户设置类型
export interface UserSettings {
  autoSaveHistory: boolean
  maxHistoryCount: number
  enableNotification: boolean
}

// 历史记录管理
export const historyManager = {
  // 获取历史记录
  getHistory(): HistoryItem[] {
    try {
      const historyStr = uni.getStorageSync(STORAGE_KEYS.CHECK_HISTORY)
      return historyStr ? JSON.parse(historyStr) : []
    } catch (error) {
      console.error('获取历史记录失败:', error)
      return []
    }
  },

  // 添加历史记录
  addHistory(item: Omit<HistoryItem, 'id' | 'checkTime'>): void {
    try {
      const history = this.getHistory()
      const newItem: HistoryItem = {
        ...item,
        id: Date.now().toString(),
        checkTime: Date.now()
      }

      // 添加到列表开头
      history.unshift(newItem)

      // 限制历史记录数量（最多保存100条）
      const maxCount = this.getSettings().maxHistoryCount
      if (history.length > maxCount) {
        history.splice(maxCount)
      }

      uni.setStorageSync(STORAGE_KEYS.CHECK_HISTORY, JSON.stringify(history))
    } catch (error) {
      console.error('保存历史记录失败:', error)
      uni.showToast({
        title: '保存历史记录失败',
        icon: 'none'
      })
    }
  },

  // 删除单条历史记录
  deleteHistory(id: string): void {
    try {
      const history = this.getHistory()
      const filteredHistory = history.filter(item => item.id !== id)
      uni.setStorageSync(STORAGE_KEYS.CHECK_HISTORY, JSON.stringify(filteredHistory))
    } catch (error) {
      console.error('删除历史记录失败:', error)
      uni.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 清空历史记录
  clearHistory(): void {
    try {
      uni.removeStorageSync(STORAGE_KEYS.CHECK_HISTORY)
    } catch (error) {
      console.error('清空历史记录失败:', error)
      uni.showToast({
        title: '清空失败',
        icon: 'none'
      })
    }
  },

  // 搜索历史记录
  searchHistory(keyword: string): HistoryItem[] {
    const history = this.getHistory()
    return history.filter(item => 
      item.content.toLowerCase().includes(keyword.toLowerCase())
    )
  }
}

// 用户设置管理
export const settingsManager = {
  // 获取用户设置
  getSettings(): UserSettings {
    try {
      const settingsStr = uni.getStorageSync(STORAGE_KEYS.USER_SETTINGS)
      const defaultSettings: UserSettings = {
        autoSaveHistory: true,
        maxHistoryCount: 100,
        enableNotification: true
      }
      return settingsStr ? { ...defaultSettings, ...JSON.parse(settingsStr) } : defaultSettings
    } catch (error) {
      console.error('获取用户设置失败:', error)
      return {
        autoSaveHistory: true,
        maxHistoryCount: 100,
        enableNotification: true
      }
    }
  },

  // 更新用户设置
  updateSettings(settings: Partial<UserSettings>): void {
    try {
      const currentSettings = this.getSettings()
      const newSettings = { ...currentSettings, ...settings }
      uni.setStorageSync(STORAGE_KEYS.USER_SETTINGS, JSON.stringify(newSettings))
    } catch (error) {
      console.error('保存用户设置失败:', error)
      uni.showToast({
        title: '保存设置失败',
        icon: 'none'
      })
    }
  }
}

// 通用存储工具
export const storageUtils = {
  // 设置存储
  set(key: string, value: any): void {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
    } catch (error) {
      console.error(`存储数据失败 [${key}]:`, error)
    }
  },

  // 获取存储
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue || null
    } catch (error) {
      console.error(`获取数据失败 [${key}]:`, error)
      return defaultValue || null
    }
  },

  // 删除存储
  remove(key: string): void {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error(`删除数据失败 [${key}]:`, error)
    }
  },

  // 清空所有存储
  clear(): void {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('清空存储失败:', error)
    }
  },

  // 获取存储信息
  getInfo(): UniApp.GetStorageInfoSyncResult {
    try {
      return uni.getStorageInfoSync()
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0
      }
    }
  }
}
