<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">请输入需要核查的内容或上传图片</text>
			
			<!-- 文本输入区域 -->
			<uni-easyinput
				v-model="inputText"
				type="textarea"
				placeholder="请输入或粘贴需要核查的文本内容"
				:maxlength="2000"
				:autoHeight="true"
			/>
			
			<!-- 图片上传区域 -->
			<view class="image-upload">
				<uni-file-picker
					v-model="imageFiles"
					fileMediatype="image"
					mode="grid"
					:limit="1"
					@select="selectImage"
					@progress="uploadProgress"
					@success="uploadSuccess"
					@fail="uploadFail"
				/>
			</view>
			
			<!-- 平台特定的图片选择按钮 -->
			<!-- #ifdef MP-QQ -->
			<button class="platform-btn" @click="chooseMessageFile">从QQ会话选择图片</button>
			<!-- #endif -->
			
			<!-- 提交按钮 -->
			<button 
				type="primary" 
				:disabled="!canSubmit"
				@click="handleSubmit"
				class="submit-btn"
			>
				开始核查
			</button>
		</uni-card>
		
		<!-- 加载状态 -->
		<uni-popup ref="loadingPopup" type="center">
			<uni-load-more status="loading" :content-text="loadingText"/>
		</uni-popup>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'

export default defineComponent({
	name: 'Index',
	setup() {
		const inputText = ref('')
		const imageFiles = ref<any[]>([])
		const loadingPopup = ref<any>(null)
		const loadingText = {
			contentdown: '上拉显示更多',
			contentrefresh: '正在加载...',
			contentnomore: '没有更多数据了'
		}
		
		// 计算是否可以提交
		const canSubmit = computed(() => {
			return inputText.value.trim().length > 0 || imageFiles.value.length > 0
		})
		
		// 选择图片回调
		const selectImage = (e: any) => {
			console.log('选择图片:', e)
		}
		
		// 上传进度回调
		const uploadProgress = (e: any) => {
			console.log('上传进度:', e)
		}
		
		// 上传成功回调
		const uploadSuccess = (e: any) => {
			console.log('上传成功:', e)
		}
		
		// 上传失败回调
		const uploadFail = (e: any) => {
			console.log('上传失败:', e)
			uni.showToast({
				title: '图片上传失败',
				icon: 'none'
			})
		}
		
		// QQ平台特定的从会话选择图片
		const chooseMessageFile = () => {
			// #ifdef MP-QQ
			qq.chooseMessageFile({
				count: 1,
				type: 'image',
				success: (res) => {
					console.log('从会话选择图片成功:', res)
					const tempFiles = res.tempFiles
					if (tempFiles && tempFiles.length > 0) {
						imageFiles.value = tempFiles.map(file => ({
							url: file.path,
							name: file.name,
							size: file.size
						}))
					}
				},
				fail: (err) => {
					console.error('从会话选择图片失败:', err)
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			})
			// #endif
		}
		
		// 提交处理
		const handleSubmit = async () => {
			try {
				// 显示加载状态
				if (loadingPopup.value) {
					loadingPopup.value.open()
				}
				
				// TODO: 调用后端API进行核查
				// 这里需要根据实际API实现
				
				// 模拟API调用延迟
				await new Promise(resolve => setTimeout(resolve, 1500))
				
				// 跳转到结果页面
				uni.navigateTo({
					url: '/pages/result/result'
				})
			} catch (error) {
				console.error('核查失败:', error)
				uni.showToast({
					title: '核查失败，请重试',
					icon: 'none'
				})
			} finally {
				// 关闭加载状态
				if (loadingPopup.value) {
					loadingPopup.value.close()
				}
			}
		}
		
		return {
			inputText,
			imageFiles,
			loadingPopup,
			loadingText,
			canSubmit,
			selectImage,
			uploadProgress,
			uploadSuccess,
			uploadFail,
			chooseMessageFile,
			handleSubmit
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.image-upload {
		margin: 20rpx 0;
	}
	
	.platform-btn {
		margin: 20rpx 0;
		background-color: #12B7F5;
		color: #FFFFFF;
		
		&:active {
			background-color: #0E9BD9;
		}
	}
	
	.submit-btn {
		margin-top: 30rpx;
	}
}
</style>
