<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">请输入需要核查的内容或上传图片</text>
			
			<!-- 文本输入区域 -->
			<uni-easyinput
				v-model="inputText"
				type="textarea"
				placeholder="请输入或粘贴需要核查的文本内容"
				:maxlength="2000"
				:autoHeight="true"
			/>
			
			<!-- 图片上传区域 -->
			<view class="image-upload">
				<uni-file-picker
					v-model="imageFiles"
					fileMediatype="image"
					mode="grid"
					:limit="1"
					@select="selectImage"
					@progress="uploadProgress"
					@success="uploadSuccess"
					@fail="uploadFail"
				/>
			</view>
			
			<!-- 平台特定的图片选择按钮 -->
			<!-- #ifdef MP-QQ -->
			<button class="platform-btn" @click="chooseMessageFile">从QQ会话选择图片</button>
			<!-- #endif -->
			
			<!-- 提交按钮 -->
			<button 
				type="primary" 
				:disabled="!canSubmit"
				@click="handleSubmit"
				class="submit-btn"
			>
				开始核查
			</button>
		</uni-card>
		
		<!-- 加载状态 -->
		<uni-popup ref="loadingPopup" type="center">
			<uni-load-more status="loading" :content-text="loadingText"/>
		</uni-popup>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { mockApi } from '@/utils/api'
import { historyManager } from '@/utils/storage'
import { validateText, validateImage, compressImage } from '@/utils/common'

export default defineComponent({
	name: 'Index',
	setup() {
		const inputText = ref('')
		const imageFiles = ref<any[]>([])
		const loadingPopup = ref<any>(null)
		const isSubmitting = ref(false)

		const loadingText = {
			contentdown: '上拉显示更多',
			contentrefresh: '正在加载...',
			contentnomore: '没有更多数据了'
		}

		// 计算是否可以提交
		const canSubmit = computed(() => {
			return !isSubmitting.value && (inputText.value.trim().length > 0 || imageFiles.value.length > 0)
		})

		// 选择图片回调
		const selectImage = async (e: any) => {
			console.log('选择图片:', e)
			const files = e.tempFiles || e.tempFilePaths
			if (files && files.length > 0) {
				try {
					const file = files[0]
					const filePath = file.path || file

					// 验证图片
					const validation = await validateImage(filePath)
					if (!validation.valid) {
						uni.showToast({
							title: validation.message || '图片验证失败',
							icon: 'none'
						})
						return
					}

					// 压缩图片
					const compressedPath = await compressImage(filePath, 0.8)

					imageFiles.value = [{
						url: compressedPath,
						name: file.name || 'image.jpg',
						size: file.size || 0
					}]
				} catch (error) {
					console.error('处理图片失败:', error)
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					})
				}
			}
		}

		// 上传进度回调
		const uploadProgress = (e: any) => {
			console.log('上传进度:', e)
		}

		// 上传成功回调
		const uploadSuccess = (e: any) => {
			console.log('上传成功:', e)
		}

		// 上传失败回调
		const uploadFail = (e: any) => {
			console.log('上传失败:', e)
			uni.showToast({
				title: '图片上传失败',
				icon: 'none'
			})
		}

		// QQ平台特定的从会话选择图片
		const chooseMessageFile = () => {
			// #ifdef MP-QQ
			qq.chooseMessageFile({
				count: 1,
				type: 'image',
				success: async (res) => {
					console.log('从会话选择图片成功:', res)
					const tempFiles = res.tempFiles
					if (tempFiles && tempFiles.length > 0) {
						try {
							const file = tempFiles[0]

							// 验证图片
							const validation = await validateImage(file.path)
							if (!validation.valid) {
								uni.showToast({
									title: validation.message || '图片验证失败',
									icon: 'none'
								})
								return
							}

							// 压缩图片
							const compressedPath = await compressImage(file.path, 0.8)

							imageFiles.value = [{
								url: compressedPath,
								name: file.name,
								size: file.size
							}]
						} catch (error) {
							console.error('处理图片失败:', error)
							uni.showToast({
								title: '图片处理失败',
								icon: 'none'
							})
						}
					}
				},
				fail: (err) => {
					console.error('从会话选择图片失败:', err)
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			})
			// #endif
		}

		// 提交处理
		const handleSubmit = async () => {
			try {
				isSubmitting.value = true

				// 显示加载状态
				if (loadingPopup.value) {
					loadingPopup.value.open()
				}

				let result: any
				let content: string
				let type: 'text' | 'image'

				if (imageFiles.value.length > 0) {
					// 图片核查
					const imagePath = imageFiles.value[0].url
					result = await mockApi.checkImage(imagePath)
					content = `图片核查: ${imageFiles.value[0].name}`
					type = 'image'
				} else {
					// 文本核查
					const text = inputText.value.trim()

					// 验证文本
					const validation = validateText(text)
					if (!validation.valid) {
						uni.showToast({
							title: validation.message || '输入验证失败',
							icon: 'none'
						})
						return
					}

					result = await mockApi.checkText(text)
					content = text
					type = 'text'
				}

				// 保存到历史记录
				historyManager.addHistory({
					content: content.length > 100 ? content.substring(0, 100) + '...' : content,
					type,
					status: result.status,
					result
				})

				// 跳转到结果页面，传递结果数据
				uni.navigateTo({
					url: '/pages/result/result',
					success: (res) => {
						// 通过事件通道传递数据
						if (res.eventChannel) {
							res.eventChannel.emit('checkResult', result)
						}
					}
				})

				// 清空输入
				inputText.value = ''
				imageFiles.value = []

			} catch (error) {
				console.error('核查失败:', error)
				uni.showToast({
					title: '核查失败，请重试',
					icon: 'none'
				})
			} finally {
				isSubmitting.value = false
				// 关闭加载状态
				if (loadingPopup.value) {
					loadingPopup.value.close()
				}
			}
		}

		return {
			inputText,
			imageFiles,
			loadingPopup,
			loadingText,
			canSubmit,
			isSubmitting,
			selectImage,
			uploadProgress,
			uploadSuccess,
			uploadFail,
			chooseMessageFile,
			handleSubmit
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.image-upload {
		margin: 20rpx 0;
	}
	
	.platform-btn {
		margin: 20rpx 0;
		background-color: #12B7F5;
		color: #FFFFFF;
		
		&:active {
			background-color: #0E9BD9;
		}
	}
	
	.submit-btn {
		margin-top: 30rpx;
	}
}
</style>
