<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<!-- 核查结果摘要 -->
			<view class="result-summary">
				<view class="status-tag" :class="resultStatus.class">
					<uni-icons :type="resultStatus.icon" size="18" color="#FFFFFF" />
					<text class="status-text">{{ resultStatus.text }}</text>
				</view>
				
				<view class="summary-text">
					<text class="uni-h5">{{ summary }}</text>
				</view>
			</view>
			
			<!-- 原始发布时间 -->
			<view class="info-section" v-if="publishTime">
				<view class="section-title">
					<uni-icons type="calendar" size="16" />
					<text class="title-text">原始发布时间</text>
				</view>
				<text class="info-content">{{ publishTime }}</text>
			</view>
			
			<!-- 信息来源 -->
			<view class="info-section" v-if="sources.length > 0">
				<view class="section-title">
					<uni-icons type="link" size="16" />
					<text class="title-text">信息来源</text>
				</view>
				<view class="source-list">
					<view 
						v-for="(source, index) in sources" 
						:key="index"
						class="source-item"
						@click="openUrl(source.url)"
					>
						<text class="source-title">{{ source.title }}</text>
						<text class="source-url">{{ source.url }}</text>
					</view>
				</view>
			</view>
			
			<!-- 相关证据 -->
			<view class="info-section" v-if="evidences.length > 0">
				<view class="section-title">
					<uni-icons type="info" size="16" />
					<text class="title-text">相关证据</text>
				</view>
				<view class="evidence-list">
					<text 
						v-for="(evidence, index) in evidences" 
						:key="index"
						class="evidence-item"
					>{{ evidence }}</text>
				</view>
			</view>
		</uni-card>
		
		<!-- 反馈按钮 -->
		<view class="feedback-section">
			<text class="feedback-title">这个结果对您有帮助吗？</text>
			<view class="feedback-buttons">
				<button 
					class="feedback-btn"
					:class="{ active: feedback === 'accurate' }"
					@click="handleFeedback('accurate')"
				>
					<uni-icons type="thumb-up" size="16" />
					<text>准确</text>
				</button>
				<button 
					class="feedback-btn"
					:class="{ active: feedback === 'inaccurate' }"
					@click="handleFeedback('inaccurate')"
				>
					<uni-icons type="thumb-down" size="16" />
					<text>不准确</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue'
import { copyToClipboard } from '@/utils/common'

export default defineComponent({
	name: 'Result',
	setup() {
		// 核查结果数据
		const checkResult = ref({
			id: '',
			status: 'supported', // supported, disputed, insufficient
			summary: '经核查，该信息基本属实。多个可靠来源证实了该事件的真实性。',
			publishTime: '2024年2月15日',
			sources: [
				{
					title: '新华网：相关事件报道',
					url: 'https://news.example.com/article1'
				},
				{
					title: '人民网：事件追踪报道',
					url: 'https://news.example.com/article2'
				}
			],
			evidences: [
				'多家权威媒体对该事件进行了报道',
				'事件发生地相关部门已确认该信息',
				'有现场照片和视频佐证'
			]
		})

		// 监听页面传递的数据
		onMounted(() => {
			const eventChannel = getCurrentPages()[getCurrentPages().length - 1].$eventChannel
			if (eventChannel) {
				eventChannel.on('checkResult', (data: any) => {
					console.log('接收到核查结果:', data)
					checkResult.value = data
				})
			}
		})
		
		// 计算结果状态显示
		const resultStatus = computed(() => {
			const statusMap = {
				supported: {
					text: '信息可信',
					icon: 'checkmarkempty',
					class: 'status-supported'
				},
				disputed: {
					text: '信息存疑',
					icon: 'warning',
					class: 'status-disputed'
				},
				insufficient: {
					text: '信息不足',
					icon: 'help',
					class: 'status-insufficient'
				}
			}
			return statusMap[checkResult.value.status]
		})
		
		// 反馈状态
		const feedback = ref('')
		
		// 处理反馈
		const handleFeedback = async (type: 'accurate' | 'inaccurate') => {
			if (feedback.value === type) {
				return
			}
			
			feedback.value = type
			
			try {
				// TODO: 调用反馈API
				await new Promise(resolve => setTimeout(resolve, 500))
				
				uni.showToast({
					title: '感谢您的反馈',
					icon: 'success'
				})
			} catch (error) {
				console.error('反馈提交失败:', error)
				uni.showToast({
					title: '反馈提交失败',
					icon: 'none'
				})
				feedback.value = ''
			}
		}
		
		// 打开URL
		const openUrl = async (url: string) => {
			try {
				// #ifdef H5
				window.open(url)
				// #endif

				// #ifdef MP
				await copyToClipboard(url)
				// #endif
			} catch (error) {
				console.error('打开链接失败:', error)
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				})
			}
		}
		
		return {
			summary: checkResult.value.summary,
			publishTime: checkResult.value.publishTime,
			sources: checkResult.value.sources,
			evidences: checkResult.value.evidences,
			resultStatus,
			feedback,
			handleFeedback,
			openUrl
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.result-summary {
		margin-bottom: 30rpx;
		
		.status-tag {
			display: inline-flex;
			align-items: center;
			padding: 10rpx 20rpx;
			border-radius: 8rpx;
			margin-bottom: 20rpx;
			
			.status-text {
				color: #FFFFFF;
				margin-left: 10rpx;
				font-size: 28rpx;
			}
		}
		
		.status-supported {
			background-color: #07C160;
		}
		
		.status-disputed {
			background-color: #FA5151;
		}
		
		.status-insufficient {
			background-color: #FFC300;
		}
		
		.summary-text {
			line-height: 1.6;
		}
	}
	
	.info-section {
		margin: 30rpx 0;
		
		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			
			.title-text {
				margin-left: 10rpx;
				font-weight: bold;
			}
		}
		
		.info-content {
			color: #666666;
		}
		
		.source-list {
			.source-item {
				margin: 20rpx 0;
				padding: 20rpx;
				background-color: #F8F8F8;
				border-radius: 8rpx;
				
				.source-title {
					display: block;
					font-size: 28rpx;
					margin-bottom: 10rpx;
				}
				
				.source-url {
					display: block;
					font-size: 24rpx;
					color: #007AFF;
				}
			}
		}
		
		.evidence-list {
			.evidence-item {
				display: block;
				margin: 10rpx 0;
				color: #666666;
				font-size: 28rpx;
				line-height: 1.6;
			}
		}
	}
	
	.feedback-section {
		margin-top: 40rpx;
		text-align: center;
		
		.feedback-title {
			font-size: 28rpx;
			color: #666666;
		}
		
		.feedback-buttons {
			display: flex;
			justify-content: center;
			margin-top: 20rpx;
			
			.feedback-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 20rpx;
				padding: 10rpx 30rpx;
				background-color: #F8F8F8;
				border: none;
				border-radius: 8rpx;
				
				&.active {
					background-color: #007AFF;
					color: #FFFFFF;
				}
				
				text {
					margin-left: 10rpx;
				}
			}
		}
	}
}
</style> 