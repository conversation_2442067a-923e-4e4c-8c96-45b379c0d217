# 快速重构指南

## 🎯 重构目标

将当前的混合结构重构为标准的前后端分离结构：

**当前结构** (不规范):
```
news-verifier-ai/
├── src/              # uni-app前端代码
├── cloudfunctions/   # 云函数后端代码
├── package.json      # 混合依赖
└── ...
```

**目标结构** (标准):
```
news-verifier-ai/
├── frontend/         # 前端项目
│   ├── src/         # uni-app源码
│   └── package.json # 前端依赖
├── backend/          # 后端项目
│   ├── cloudfunctions/ # 云函数
│   └── package.json # 后端依赖
├── docs/            # 文档
└── package.json     # 项目管理
```

## 🚀 一键重构

### 方法1: 自动化脚本 (推荐)

```bash
# 运行重构脚本
node restructure.js
```

### 方法2: 手动重构

```bash
# 1. 创建目录结构
mkdir -p frontend/src backend/cloudfunctions docs tools tests

# 2. 移动前端文件
mv src frontend/
mv package.json frontend/
mv vite.config.ts frontend/
mv tsconfig.json frontend/
mv manifest.json frontend/
mv project.config.json frontend/

# 3. 移动后端文件
mv cloudfunctions backend/

# 4. 移动文档文件
mv docs/* docs/
mv *.md docs/
```

## ⚙️ 配置更新

### 1. 更新前端配置

**frontend/project.config.json**:
```json
{
  "cloudfunctionRoot": "../backend/cloudfunctions/",
  "miniprogramRoot": "./"
}
```

### 2. 创建根目录配置

**package.json**:
```json
{
  "name": "news-verifier-ai",
  "scripts": {
    "dev:frontend": "cd frontend && npm run dev",
    "deploy:backend": "cd backend && npm run deploy",
    "install:all": "npm install && cd frontend && npm install"
  },
  "workspaces": ["frontend", "backend"]
}
```

### 3. 创建后端配置

**backend/package.json**:
```json
{
  "name": "news-verifier-backend",
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

## 🔧 开发工作流

### 重构后的开发流程

```bash
# 安装所有依赖
npm run install:all

# 前端开发
npm run dev:frontend

# 后端部署 (在微信开发者工具中)
# 打开 backend 目录，部署云函数
```

### VS Code 工作区

使用提供的 `news-verifier-ai.code-workspace` 文件：
- 多文件夹工作区
- 统一的任务和调试配置
- 推荐的扩展

## 📁 目录说明

### frontend/ - 前端项目
- **src/**: uni-app源码
- **dist/**: 构建输出
- **package.json**: 前端依赖管理

### backend/ - 后端项目
- **cloudfunctions/**: 云函数代码
- **config/**: 配置文件
- **scripts/**: 部署脚本

### docs/ - 文档
- **api/**: API文档
- **deployment/**: 部署文档
- **development/**: 开发文档

## ✅ 重构检查清单

### 文件移动
- [ ] src/ → frontend/src/
- [ ] cloudfunctions/ → backend/cloudfunctions/
- [ ] 配置文件移动到对应目录
- [ ] 文档整理到 docs/

### 配置更新
- [ ] 更新 project.config.json 中的云函数路径
- [ ] 创建根目录 package.json
- [ ] 创建 backend/package.json
- [ ] 更新 .gitignore

### 功能测试
- [ ] 前端开发服务器启动正常
- [ ] 云函数部署正常
- [ ] 小程序功能测试通过

## 🎯 重构优势

### 1. 清晰的职责分离
- 前端专注于UI和用户体验
- 后端专注于业务逻辑和数据处理

### 2. 独立的开发和部署
- 前端可以独立构建和部署
- 后端云函数可以独立更新

### 3. 团队协作友好
- 不同角色可以专注各自领域
- 减少代码冲突和依赖问题

### 4. 扩展性强
- 易于添加新的前端平台
- 易于添加新的后端服务

## 🚨 注意事项

### 重构前备份
```bash
# 创建备份
cp -r . ../news-verifier-ai-backup
```

### 路径更新
- 检查所有相对路径引用
- 更新导入语句
- 验证资源文件路径

### 依赖管理
- 前后端依赖分离
- 使用 workspaces 统一管理
- 避免重复安装

## 🆘 常见问题

### Q: 重构后云函数无法部署？
A: 检查 project.config.json 中的 cloudfunctionRoot 路径是否正确

### Q: 前端启动失败？
A: 确保在 frontend 目录下安装了依赖：`cd frontend && npm install`

### Q: 文件找不到？
A: 检查文件是否正确移动到新的目录结构中

### Q: VS Code 无法识别项目？
A: 使用 `news-verifier-ai.code-workspace` 文件打开工作区

## 📞 技术支持

如果重构过程中遇到问题：
1. 查看详细的重构日志
2. 检查文件移动是否完整
3. 验证配置文件是否正确更新
4. 参考 `项目结构重构指南.md` 获取详细信息

---

🎉 **重构完成后，您将拥有一个标准化、可维护、易扩展的项目结构！**
