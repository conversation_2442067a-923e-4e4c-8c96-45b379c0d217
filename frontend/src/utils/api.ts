/**
 * API 服务层
 * 统一管理所有的API调用
 */

import { handleNetworkError, handleApiError, AppError, ErrorType } from './error'

// API 基础配置
const API_CONFIG = {
  baseUrl: 'https://your-api-domain.com/api/v1', // 替换为实际的API地址
  timeout: 30000, // 30秒超时
}

// 请求拦截器
const request = (options: UniApp.RequestOptions) => {
  return new Promise((resolve, reject) => {
    // 显示加载状态
    uni.showLoading({
      title: '处理中...',
      mask: true
    })

    // 设置默认配置
    const config: UniApp.RequestOptions = {
      url: `${API_CONFIG.baseUrl}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: API_CONFIG.timeout,
      success: (res) => {
        uni.hideLoading()
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          const error = new AppError(
            `请求失败: ${res.statusCode}`,
            ErrorType.API_ERROR,
            res.statusCode
          )
          handleApiError(error, res.statusCode)
          reject(error)
        }
      },
      fail: (err) => {
        uni.hideLoading()
        handleNetworkError(err)
        reject(err)
      }
    }

    uni.request(config)
  })
}

// 文件上传请求
const uploadFile = (filePath: string, name: string = 'file') => {
  return new Promise((resolve, reject) => {
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    uni.uploadFile({
      url: `${API_CONFIG.baseUrl}/check/image`,
      filePath,
      name,
      header: {
        'Authorization': 'Bearer your-token' // 如果需要认证
      },
      success: (res) => {
        uni.hideLoading()
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            resolve(data)
          } catch (e) {
            const error = new AppError('响应数据格式错误', ErrorType.API_ERROR)
            handleApiError(error)
            reject(error)
          }
        } else {
          const error = new AppError(
            `上传失败: ${res.statusCode}`,
            ErrorType.API_ERROR,
            res.statusCode
          )
          handleApiError(error, res.statusCode)
          reject(error)
        }
      },
      fail: (err) => {
        uni.hideLoading()
        handleNetworkError(err)
        reject(err)
      }
    })
  })
}

// 微信云开发API接口定义
export const api = {
  // 文本核查 - 调用云函数
  checkText: (text: string) => {
    return new Promise((resolve, reject) => {
      uni.showLoading({
        title: '核查中...',
        mask: true
      })

      wx.cloud.callFunction({
        name: 'checkText',
        data: { text },
        success: (res) => {
          uni.hideLoading()
          if (res.result.code === 200) {
            resolve(res.result.data)
          } else {
            reject(new Error(res.result.message || '核查失败'))
          }
        },
        fail: (err) => {
          uni.hideLoading()
          handleNetworkError(err)
          reject(err)
        }
      })
    })
  },

  // 图片核查 - 先上传图片再调用云函数
  checkImage: (filePath: string) => {
    return new Promise((resolve, reject) => {
      uni.showLoading({
        title: '上传图片...',
        mask: true
      })

      // 先上传图片到云存储
      wx.cloud.uploadFile({
        cloudPath: `images/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`,
        filePath: filePath,
        success: (uploadRes) => {
          uni.showLoading({
            title: '核查中...',
            mask: true
          })

          // 调用图片核查云函数
          wx.cloud.callFunction({
            name: 'checkImage',
            data: { fileID: uploadRes.fileID },
            success: (res) => {
              uni.hideLoading()
              if (res.result.code === 200) {
                resolve(res.result.data)
              } else {
                reject(new Error(res.result.message || '核查失败'))
              }
            },
            fail: (err) => {
              uni.hideLoading()
              handleNetworkError(err)
              reject(err)
            }
          })
        },
        fail: (err) => {
          uni.hideLoading()
          handleNetworkError(err)
          reject(err)
        }
      })
    })
  },

  // 提交反馈 - 调用云函数
  submitFeedback: (checkId: string, rating: 'accurate' | 'inaccurate') => {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'submitFeedback',
        data: { check_id: checkId, rating },
        success: (res) => {
          if (res.result.code === 200) {
            resolve(res.result.data)
          } else {
            reject(new Error(res.result.message || '提交失败'))
          }
        },
        fail: (err) => {
          handleNetworkError(err)
          reject(err)
        }
      })
    })
  },

  // 获取历史记录 - 调用云对象
  getHistory: (page: number = 1, limit: number = 20) => {
    return new Promise((resolve, reject) => {
      wx.cloud.callContainer({
        name: 'newsVerifier',
        method: 'getUserHistory',
        data: { page, limit },
        success: (res) => {
          if (res.result.success) {
            resolve(res.result.data)
          } else {
            reject(new Error(res.result.error || '获取历史记录失败'))
          }
        },
        fail: (err) => {
          handleNetworkError(err)
          reject(err)
        }
      })
    })
  }
}

// 模拟API响应（开发阶段使用）
export const mockApi = {
  checkText: async (text: string) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    return {
      id: Date.now().toString(),
      status: 'supported',
      summary: '经核查，该信息基本属实。多个可靠来源证实了该事件的真实性。',
      publishTime: '2024年2月15日',
      sources: [
        {
          title: '新华网：相关事件报道',
          url: 'https://news.example.com/article1'
        },
        {
          title: '人民网：事件追踪报道',
          url: 'https://news.example.com/article2'
        }
      ],
      evidences: [
        '多家权威媒体对该事件进行了报道',
        '事件发生地相关部门已确认该信息',
        '有现场照片和视频佐证'
      ]
    }
  },

  checkImage: async (filePath: string) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    return {
      id: Date.now().toString(),
      status: 'disputed',
      summary: '经核查，该图片信息存在争议。部分信息可能被断章取义或存在时间错位。',
      publishTime: '2024年1月20日',
      sources: [
        {
          title: '辟谣平台：相关图片核实',
          url: 'https://fact-check.example.com/article1'
        }
      ],
      evidences: [
        '原图片拍摄时间与传播时间不符',
        '图片内容与描述存在偏差',
        '缺乏权威来源证实'
      ]
    }
  },

  submitFeedback: async (checkId: string, rating: string) => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return { success: true }
  }
}
