<template>
	<view class="container">
		<uni-card v-if="historyList.length === 0" :is-shadow="false" is-full>
			<view class="empty-state">
				<uni-icons type="info" size="64" color="#999999" />
				<text class="empty-text">暂无核查记录</text>
			</view>
		</uni-card>
		
		<template v-else>
			<uni-swipe-action>
				<uni-swipe-action-item
					v-for="(item, index) in historyList"
					:key="index"
					:right-options="swipeOptions"
					@click="handleSwipeClick($event, item)"
				>
					<uni-card :is-shadow="false" is-full>
						<view class="history-item" @click="viewDetail(item)">
							<!-- 状态标签 -->
							<view class="status-tag" :class="getStatusClass(item.status)">
								<text class="status-text">{{ getStatusText(item.status) }}</text>
							</view>
							
							<!-- 内容摘要 -->
							<view class="content-summary">
								<text class="summary-text">{{ item.content }}</text>
							</view>
							
							<!-- 时间信息 -->
							<view class="time-info">
								<text class="check-time">{{ formatTime(item.checkTime) }}</text>
							</view>
						</view>
					</uni-card>
				</uni-swipe-action-item>
			</uni-swipe-action>
			
			<!-- 清空历史按钮 -->
			<view class="clear-history">
				<button 
					type="warn" 
					size="mini" 
					@click="showClearConfirm"
				>清空历史记录</button>
			</view>
		</template>
		
		<!-- 下拉刷新提示 -->
		<uni-load-more :status="loadMoreStatus" />
	</view>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { historyManager, type HistoryItem } from '@/utils/storage'
import { formatRelativeTime, showConfirm } from '@/utils/common'

export default defineComponent({
	name: 'History',
	setup() {
		// 历史记录列表
		const historyList = ref<HistoryItem[]>([])

		// 加载状态
		const loadMoreStatus = ref<'more' | 'loading' | 'noMore'>('more')

		// 滑动操作选项
		const swipeOptions = [
			{
				text: '删除',
				style: {
					backgroundColor: '#FA5151'
				}
			}
		]

		// 获取历史记录
		const loadHistory = () => {
			try {
				historyList.value = historyManager.getHistory()
			} catch (error) {
				console.error('加载历史记录失败:', error)
				uni.showToast({
					title: '加载历史记录失败',
					icon: 'none'
				})
			}
		}

		// 删除单条历史记录
		const deleteHistory = async (item: HistoryItem) => {
			const confirmed = await showConfirm(
				'确认删除',
				'是否删除这条历史记录？',
				'删除',
				'取消'
			)

			if (confirmed) {
				historyManager.deleteHistory(item.id)
				loadHistory() // 重新加载列表
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
			}
		}

		// 清空历史记录
		const clearHistory = async () => {
			const confirmed = await showConfirm(
				'确认清空',
				'是否清空所有历史记录？此操作不可恢复。',
				'清空',
				'取消'
			)

			if (confirmed) {
				historyManager.clearHistory()
				historyList.value = []
				uni.showToast({
					title: '已清空历史记录',
					icon: 'success'
				})
			}
		}
		
		// 查看详情
		const viewDetail = (item: HistoryItem) => {
			// 将结果数据传递到结果页面
			uni.navigateTo({
				url: '/pages/result/result',
				success: (res) => {
					// 传递数据给结果页面
					if (res.eventChannel) {
						res.eventChannel.emit('checkResult', item.result)
					}
				}
			})
		}

		// 处理滑动操作点击
		const handleSwipeClick = (e: {content: {text: string}}, item: HistoryItem) => {
			if (e.content.text === '删除') {
				deleteHistory(item)
			}
		}

		// 获取状态样式类
		const getStatusClass = (status: string) => {
			const classMap = {
				supported: 'status-supported',
				disputed: 'status-disputed',
				insufficient: 'status-insufficient'
			}
			return classMap[status] || ''
		}

		// 获取状态文本
		const getStatusText = (status: string) => {
			const textMap = {
				supported: '信息可信',
				disputed: '信息存疑',
				insufficient: '信息不足'
			}
			return textMap[status] || ''
		}

		// 格式化时间 - 使用相对时间
		const formatTime = (timestamp: number) => {
			return formatRelativeTime(timestamp)
		}
		
		// 监听下拉刷新
		uni.onPullDownRefresh(() => {
			loadHistory()
			setTimeout(() => {
				uni.stopPullDownRefresh()
			}, 500)
		})
		
		// 页面加载时获取历史记录
		onMounted(() => {
			loadHistory()
		})
		
		return {
			historyList,
			loadMoreStatus,
			swipeOptions,
			handleSwipeClick,
			viewDetail,
			getStatusClass,
			getStatusText,
			formatTime,
			showClearConfirm: clearHistory
		}
	}
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 0;
		
		.empty-text {
			margin-top: 20rpx;
			color: #999999;
			font-size: 28rpx;
		}
	}
	
	.history-item {
		.status-tag {
			display: inline-block;
			padding: 4rpx 12rpx;
			border-radius: 4rpx;
			margin-bottom: 16rpx;
			
			.status-text {
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}
		
		.status-supported {
			background-color: #07C160;
		}
		
		.status-disputed {
			background-color: #FA5151;
		}
		
		.status-insufficient {
			background-color: #FFC300;
		}
		
		.content-summary {
			margin: 16rpx 0;
			
			.summary-text {
				font-size: 28rpx;
				color: #333333;
				line-height: 1.6;
				
				// 最多显示两行
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
		}
		
		.time-info {
			.check-time {
				font-size: 24rpx;
				color: #999999;
			}
		}
	}
	
	.clear-history {
		margin: 40rpx 0;
		text-align: center;
	}
}
</style> 