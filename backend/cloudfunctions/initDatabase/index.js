// 数据库初始化云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  const db = cloud.database()
  
  try {
    // 创建核查记录集合
    await createCheckRecordsCollection(db)
    
    // 创建用户反馈集合
    await createUserFeedbackCollection(db)
    
    // 创建系统配置集合
    await createSystemConfigCollection(db)
    
    return {
      success: true,
      message: '数据库初始化完成'
    }
    
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 创建核查记录集合
async function createCheckRecordsCollection(db) {
  try {
    // 检查集合是否已存在
    const collections = await db.listCollections()
    const exists = collections.data.some(col => col.name === 'check_records')
    
    if (!exists) {
      await db.createCollection('check_records')
      console.log('创建 check_records 集合成功')
    }
    
    // 创建索引
    await db.collection('check_records').createIndex({
      keys: { user_openid: 1, created_at: -1 },
      name: 'user_time_index'
    })
    
    await db.collection('check_records').createIndex({
      keys: { id: 1 },
      name: 'check_id_index',
      unique: true
    })
    
    await db.collection('check_records').createIndex({
      keys: { status: 1 },
      name: 'status_index'
    })
    
    console.log('check_records 索引创建完成')
    
  } catch (error) {
    console.error('创建 check_records 集合失败:', error)
    throw error
  }
}

// 创建用户反馈集合
async function createUserFeedbackCollection(db) {
  try {
    const collections = await db.listCollections()
    const exists = collections.data.some(col => col.name === 'user_feedback')
    
    if (!exists) {
      await db.createCollection('user_feedback')
      console.log('创建 user_feedback 集合成功')
    }
    
    // 创建索引
    await db.collection('user_feedback').createIndex({
      keys: { check_id: 1 },
      name: 'check_id_index'
    })
    
    await db.collection('user_feedback').createIndex({
      keys: { user_openid: 1, created_at: -1 },
      name: 'user_time_index'
    })
    
    await db.collection('user_feedback').createIndex({
      keys: { id: 1 },
      name: 'feedback_id_index',
      unique: true
    })
    
    console.log('user_feedback 索引创建完成')
    
  } catch (error) {
    console.error('创建 user_feedback 集合失败:', error)
    throw error
  }
}

// 创建系统配置集合
async function createSystemConfigCollection(db) {
  try {
    const collections = await db.listCollections()
    const exists = collections.data.some(col => col.name === 'system_config')
    
    if (!exists) {
      await db.createCollection('system_config')
      console.log('创建 system_config 集合成功')
      
      // 插入默认配置
      await db.collection('system_config').add({
        data: {
          key: 'app_settings',
          value: {
            max_text_length: 2000,
            min_text_length: 5,
            max_image_size: 10 * 1024 * 1024, // 10MB
            supported_image_formats: ['jpg', 'jpeg', 'png', 'webp'],
            rate_limit: {
              per_user_per_hour: 100,
              per_user_per_day: 500
            },
            ocr_config: {
              enabled: true,
              confidence_threshold: 0.8
            },
            llm_config: {
              enabled: true,
              model: 'gpt-3.5-turbo',
              max_tokens: 1000
            }
          },
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      
      await db.collection('system_config').add({
        data: {
          key: 'api_keys',
          value: {
            ocr_service: 'your_ocr_api_key',
            llm_service: 'your_llm_api_key',
            search_service: 'your_search_api_key'
          },
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      
      console.log('系统配置初始化完成')
    }
    
    // 创建索引
    await db.collection('system_config').createIndex({
      keys: { key: 1 },
      name: 'config_key_index',
      unique: true
    })
    
  } catch (error) {
    console.error('创建 system_config 集合失败:', error)
    throw error
  }
}
