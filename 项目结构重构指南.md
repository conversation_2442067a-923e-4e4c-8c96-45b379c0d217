# 项目结构重构指南

## 推荐的标准项目结构

```
news-verifier-ai/                    # 项目根目录
├── README.md                        # 项目说明文档
├── .gitignore                       # Git忽略文件
├── package.json                     # 项目依赖管理
├── 
├── frontend/                        # 前端目录
│   ├── src/                         # uni-app源码
│   │   ├── components/              # 组件
│   │   ├── pages/                   # 页面
│   │   ├── static/                  # 静态资源
│   │   ├── utils/                   # 工具函数
│   │   ├── App.vue                  # 应用入口
│   │   ├── main.ts                  # 主文件
│   │   └── pages.json               # 页面配置
│   ├── dist/                        # 构建输出
│   ├── package.json                 # 前端依赖
│   ├── vite.config.ts              # 构建配置
│   ├── tsconfig.json               # TypeScript配置
│   ├── manifest.json               # 应用配置
│   └── project.config.json         # 微信小程序配置
│
├── backend/                         # 后端目录
│   ├── cloudfunctions/              # 云函数
│   │   ├── checkText/               # 文本核查
│   │   │   ├── index.js
│   │   │   └── package.json
│   │   ├── checkImage/              # 图片核查
│   │   │   ├── index.js
│   │   │   └── package.json
│   │   ├── submitFeedback/          # 用户反馈
│   │   │   ├── index.js
│   │   │   └── package.json
│   │   ├── initDatabase/            # 数据库初始化
│   │   │   ├── index.js
│   │   │   └── package.json
│   │   └── newsVerifier/            # 云对象
│   │       ├── index.obj.js
│   │       └── package.json
│   ├── database/                    # 数据库相关
│   │   ├── schemas/                 # 数据库模式
│   │   │   ├── check_records.json
│   │   │   ├── user_feedback.json
│   │   │   └── system_config.json
│   │   └── migrations/              # 数据迁移脚本
│   ├── config/                      # 配置文件
│   │   ├── env.js                   # 环境配置
│   │   └── database.js              # 数据库配置
│   └── scripts/                     # 部署脚本
│       ├── deploy.js                # 部署脚本
│       └── init.js                  # 初始化脚本
│
├── docs/                            # 文档目录
│   ├── api/                         # API文档
│   │   ├── README.md
│   │   ├── 云函数API.md
│   │   └── 云对象API.md
│   ├── deployment/                  # 部署文档
│   │   ├── 微信云开发部署指南.md
│   │   └── 快速开始.md
│   ├── development/                 # 开发文档
│   │   ├── 前端开发指南.md
│   │   ├── 后端开发指南.md
│   │   └── 第三方服务集成.md
│   └── design/                      # 设计文档
│       ├── 系统架构.md
│       ├── 数据库设计.md
│       └── API设计.md
│
├── tools/                           # 工具脚本
│   ├── build.js                     # 构建脚本
│   ├── deploy.js                    # 部署脚本
│   └── test.js                      # 测试脚本
│
├── tests/                           # 测试目录
│   ├── frontend/                    # 前端测试
│   ├── backend/                     # 后端测试
│   └── e2e/                         # 端到端测试
│
└── .vscode/                         # VS Code配置
    ├── settings.json
    ├── launch.json
    └── extensions.json
```

## 重构步骤

### 1. 创建新的目录结构

```bash
# 在项目根目录执行
mkdir -p frontend backend/cloudfunctions backend/database/schemas backend/database/migrations
mkdir -p backend/config backend/scripts docs/api docs/deployment docs/development docs/design
mkdir -p tools tests/frontend tests/backend tests/e2e .vscode
```

### 2. 移动前端文件

```bash
# 移动uni-app相关文件到frontend目录
mv src/ frontend/
mv static/ frontend/src/
mv package.json frontend/
mv vite.config.ts frontend/
mv tsconfig.json frontend/
mv manifest.json frontend/
mv project.config.json frontend/
```

### 3. 移动后端文件

```bash
# 移动云函数到backend目录
mv cloudfunctions/ backend/
```

### 4. 更新配置文件

需要更新以下配置：

#### frontend/project.config.json
```json
{
  "cloudfunctionRoot": "../backend/cloudfunctions/",
  "miniprogramRoot": "./",
  "compileType": "miniprogram"
}
```

#### frontend/vite.config.ts
```typescript
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  root: './',
  build: {
    outDir: 'dist'
  }
})
```

## 优势分析

### 1. 清晰的模块划分
- **frontend/**: 专门存放前端代码，独立的构建和部署
- **backend/**: 专门存放后端代码，包含云函数、数据库等
- **docs/**: 统一的文档管理
- **tools/**: 开发和部署工具

### 2. 独立开发
- 前端和后端可以独立开发和测试
- 不同团队成员可以专注于各自领域
- 便于版本控制和代码审查

### 3. 部署灵活性
- 前端可以独立部署到不同平台
- 后端云函数可以独立更新
- 支持灰度发布和回滚

### 4. 扩展性
- 易于添加新的前端平台（H5、App等）
- 易于添加新的后端服务
- 便于集成CI/CD流程

## 配置文件调整

### 根目录 package.json
```json
{
  "name": "news-verifier-ai",
  "version": "1.0.0",
  "description": "AI赋能群聊信息核查助手",
  "scripts": {
    "dev:frontend": "cd frontend && npm run dev",
    "build:frontend": "cd frontend && npm run build",
    "deploy:backend": "cd backend && npm run deploy",
    "deploy:all": "npm run build:frontend && npm run deploy:backend",
    "test": "npm run test:frontend && npm run test:backend",
    "test:frontend": "cd frontend && npm run test",
    "test:backend": "cd backend && npm run test"
  },
  "workspaces": [
    "frontend",
    "backend"
  ]
}
```

### frontend/package.json
```json
{
  "name": "news-verifier-frontend",
  "version": "1.0.0",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "@dcloudio/uni-app": "^3.0.0",
    "vue": "^3.0.0"
  }
}
```

### backend/package.json
```json
{
  "name": "news-verifier-backend",
  "version": "1.0.0",
  "scripts": {
    "deploy": "node scripts/deploy.js",
    "init": "node scripts/init.js",
    "test": "jest"
  },
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

## 开发工作流

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### 后端开发
```bash
cd backend
# 在微信开发者工具中打开backend目录
# 或使用云开发CLI工具
```

### 全栈开发
```bash
# 根目录安装依赖
npm install

# 启动前端开发服务器
npm run dev:frontend

# 部署后端服务
npm run deploy:backend
```

## 版本控制

### .gitignore 调整
```
# 依赖
node_modules/
frontend/node_modules/
backend/node_modules/

# 构建输出
frontend/dist/
frontend/unpackage/

# 环境配置
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/

# 日志
*.log
npm-debug.log*

# 临时文件
.DS_Store
Thumbs.db
```

## 迁移建议

### 立即执行
1. 创建新的目录结构
2. 移动现有文件到对应目录
3. 更新配置文件路径
4. 测试前端和后端功能

### 渐进式迁移
如果项目已经在开发中，可以：
1. 先创建新结构的分支
2. 逐步迁移文件
3. 更新CI/CD配置
4. 团队培训新的工作流程

## 总结

这种结构的优势：
- ✅ **职责清晰**: 前后端分离，便于团队协作
- ✅ **扩展性强**: 易于添加新平台和服务
- ✅ **维护性好**: 独立的构建和部署流程
- ✅ **标准化**: 符合现代全栈项目的最佳实践

建议您采用这种结构来重新组织项目，这将为项目的长期发展奠定良好的基础。
